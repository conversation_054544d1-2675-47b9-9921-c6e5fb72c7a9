import { type Address } from "viem/accounts";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

/**
 * 铸造许可证代币示例
 * 演示如何为已有许可证条款的 IP 资产铸造许可证代币
 *
 * 注意：此文件现在主要用于手动铸造许可证代币
 * 如果需要在生成许可证后自动铸造代币，请使用 auto-mint-license-tokens-demo.ts
 */

async function mintLicenseTokensExample() {
  console.log("🪙 许可证代币铸造示例");
  console.log("=" .repeat(60));

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    return;
  }

  // 配置信息 - 请根据实际情况修改
  const CONFIG = {
    // 已经注册并附加了许可证条款的 IP 资产
    ipId: "******************************************" as Address, // 替换为你的 IP ID

    // 铸造配置
    licenseType: "COMMERCIAL_USE" as "COMMERCIAL_USE" | "LIMITED_COMMERCIAL", // 许可证类型
    amount: 1, // 铸造数量

    // 接收者地址（可选）
    receiver: undefined as Address | undefined, // 如果不指定，代币发送给发送交易的钱包

    useMainnet: false // 是否使用主网
  };

  try {
    // 创建管理器实例
    console.log("🔧 初始化许可证管理器...");
    const manager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      CONFIG.useMainnet
    );
    console.log(`✅ 管理器初始化完成 (${CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log("");

    console.log("📋 铸造配置:");
    console.log(`   IP 资产 ID: ${CONFIG.ipId}`);
    console.log(`   许可证类型: ${CONFIG.licenseType}`);
    console.log(`   铸造数量: ${CONFIG.amount}`);
    console.log(`   接收者: ${CONFIG.receiver || "发送交易的钱包地址"}`);
    console.log("");

    // 铸造许可证代币
    console.log("🪙 开始铸造许可证代币...");
    console.log("=" .repeat(50));

    // 注册许可证条款（如果还没有注册）
    await manager.registerCommercialUseLicense();
    if (CONFIG.licenseType === "LIMITED_COMMERCIAL") {
      await manager.registerLimitedCommercialLicense();
    }

    // 使用新的铸造方法
    try {
      let mintResult;

      if (CONFIG.licenseType === "COMMERCIAL_USE") {
        console.log("铸造商业使用许可证代币...");
        mintResult = await manager.mintCommercialLicenseToken(
          CONFIG.ipId,
          CONFIG.amount,
          CONFIG.receiver
        );
      } else if (CONFIG.licenseType === "LIMITED_COMMERCIAL") {
        console.log("铸造限量商业许可证代币...");
        mintResult = await manager.mintLimitedCommercialLicenseToken(
          CONFIG.ipId,
          CONFIG.amount,
          CONFIG.receiver
        );
      } else {
        throw new Error(`不支持的许可证类型: ${CONFIG.licenseType}`);
      }

      console.log("");
      console.log("🎉 许可证代币铸造成功！");
      console.log("=" .repeat(50));
      console.log(`许可证代币 IDs: ${mintResult.licenseTokenIds}`);
      console.log(`交易哈希: ${mintResult.txHash}`);
      console.log("");

    } catch (error) {
      if (error instanceof Error && error.message.includes("余额不足")) {
        console.log("❌ 铸造失败：余额不足");
        console.log("💡 建议：");
        console.log("   • 确保钱包有足够的 WIP 代币余额");
        console.log("   • 商业使用许可证需要 10 WIP");
        console.log("   • 限量商业许可证需要 50 WIP");
        console.log("   • 访问 https://faucet.story.foundation/ 获取测试代币");
      } else {
        console.log(`❌ 铸造失败: ${error}`);
      }
      throw error;
    }

    console.log("📝 代币分发规则:");
    console.log("1. 如果指定了 receiver 参数：代币发送给指定地址");
    console.log("2. 如果没有指定 receiver：代币发送给发送交易的钱包地址");
    console.log("3. 铸造费用从发送交易的钱包扣除");
    console.log("4. 收益分成会在未来商业使用时分配给 IP 资产所有者");

  } catch (error) {
    console.error("❌ 铸造失败:", error);
  }
}

// 手动铸造许可证代币的便捷函数
export async function mintLicenseTokensForIP(
  ipId: Address,
  licenseType: "COMMERCIAL_USE" | "LIMITED_COMMERCIAL",
  amount: number = 1,
  receiver?: Address,
  useMainnet: boolean = false
) {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  const manager = new MainnetCommercialLicenseManager(
    process.env.WALLET_PRIVATE_KEY,
    useMainnet
  );

  // 注册许可证条款（如果还没有注册）
  await manager.registerCommercialUseLicense();
  if (licenseType === "LIMITED_COMMERCIAL") {
    await manager.registerLimitedCommercialLicense();
  }

  // 铸造代币
  if (licenseType === "COMMERCIAL_USE") {
    return await manager.mintCommercialLicenseToken(ipId, amount, receiver);
  } else {
    return await manager.mintLimitedCommercialLicenseToken(ipId, amount, receiver);
  }
}

// 运行示例
if (require.main === module) {
  mintLicenseTokensExample().catch(console.error);
}

export { mintLicenseTokensExample };
