import { type Address } from "viem/accounts";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

/**
 * 商业许可证使用示例
 * 演示如何为不同的 NFT 添加商业使用和限量商业许可证
 */

// 示例配置 - 请根据实际情况修改
const EXAMPLE_CONFIG = {
  // 网络设置
  useMainnet: false, // 设置为 true 使用主网

  // 示例 NFT 列表
  nfts: [
    {
      name: "测试艺术品 #001",
      contract: "0xEe2E2a135eec6228562C77543643FD3BE6A174d1" as Address,
      tokenId: "1",
      includeCommercial: true,
      includeLimitedCommercial: true,
      // 自动铸造许可证代币配置
      autoMintTokens: true, // 是否自动铸造许可证代币
      mintCommercialTokens: true, // 是否铸造商业使用许可证代币
      mintLimitedCommercialTokens: true, // 是否铸造限量商业许可证代币
      tokenAmount: 1, // 每种许可证代币的铸造数量
    },
  ]
};

async function runCommercialLicenseExample() {
  console.log("🎨 商业许可证授权示例");
  console.log("=" .repeat(60));
  console.log("本示例演示如何为不同类型的 NFT 添加商业许可证");
  console.log("");

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  // 检查 NFT 配置
  const hasValidNFTs = EXAMPLE_CONFIG.nfts.some(nft =>
    !nft.contract.includes("你的NFT合约地址")
  );

  if (!hasValidNFTs) {
    console.log("⚠️ 请先配置真实的 NFT 合约地址");
    console.log("💡 修改 EXAMPLE_CONFIG.nfts 中的合约地址");
    console.log("");
    console.log("📋 当前配置的示例 NFT:");
    EXAMPLE_CONFIG.nfts.forEach((nft, index) => {
      console.log(`${index + 1}. ${nft.name}`);
      console.log(`   合约: ${nft.contract}`);
      console.log(`   Token ID: ${nft.tokenId}`);
      console.log(`   商业使用许可证: ${nft.includeCommercial ? "✅" : "❌"}`);
      console.log(`   限量商业许可证: ${nft.includeLimitedCommercial ? "✅" : "❌"}`);
      console.log("");
    });
    return;
  }

  try {
    // 创建管理器实例
    console.log("🔧 初始化商业许可证管理器...");
    const manager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      EXAMPLE_CONFIG.useMainnet
    );
    console.log(`✅ 管理器初始化完成 (${EXAMPLE_CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log("");

    // 处理每个 NFT
    for (let i = 0; i < EXAMPLE_CONFIG.nfts.length; i++) {
      const nft = EXAMPLE_CONFIG.nfts[i];

      console.log(`🖼️ 处理 NFT ${i + 1}/${EXAMPLE_CONFIG.nfts.length}: ${nft.name}`);
      console.log("=" .repeat(50));
      console.log(`合约地址: ${nft.contract}`);
      console.log(`Token ID: ${nft.tokenId}`);
      console.log(`商业使用许可证: ${nft.includeCommercial ? "包含" : "跳过"}`);
      console.log(`限量商业许可证: ${nft.includeLimitedCommercial ? "包含" : "跳过"}`);
      console.log("");

      try {
        // 为 NFT 添加商业许可证
        const result = await manager.addCommercialLicensesToNFT(
          nft.contract,
          nft.tokenId,
          nft.includeCommercial,
          nft.includeLimitedCommercial,
          false // 不强制注册新许可证
        );

        console.log(`✅ ${nft.name} 许可证添加完成`);
        console.log(`   IP ID: ${result.ipId}`);
        if (result.commercialLicenseTermsId) {
          console.log(`   商业许可证 ID: ${result.commercialLicenseTermsId}`);
        }
        if (result.limitedCommercialLicenseTermsId) {
          console.log(`   限量商业许可证 ID: ${result.limitedCommercialLicenseTermsId}`);
        }
        console.log(`   新注册的 IP: ${result.isNewIP ? "是" : "否"}`);
        console.log("");

        // 自动铸造许可证代币给交易发起者
        if (nft.autoMintTokens) {
          console.log(`🪙 开始为 ${nft.name} 自动铸造许可证代币...`);
          console.log("=" .repeat(50));

          try {
            const mintResults = await manager.mintLicenseTokensForIP(
              result.ipId,
              {
                mintCommercial: nft.mintCommercialTokens && nft.includeCommercial,
                mintLimitedCommercial: nft.mintLimitedCommercialTokens && nft.includeLimitedCommercial,
                amount: nft.tokenAmount || 1,
                receiver: undefined // 不指定接收者，代币发送给交易发起者
              }
            );

            const successfulMints = mintResults.filter(r => r.success);
            const failedMints = mintResults.filter(r => !r.success);

            console.log(`✅ ${nft.name} 许可证代币铸造完成`);
            console.log(`   成功铸造: ${successfulMints.length} 种许可证代币`);
            if (failedMints.length > 0) {
              console.log(`   失败铸造: ${failedMints.length} 种许可证代币`);
            }
            console.log("");

          } catch (mintError) {
            console.log(`⚠️ ${nft.name} 许可证代币铸造失败: ${mintError}`);
            console.log("   许可证已成功添加，但代币铸造失败");
            console.log("   💡 可以稍后手动铸造许可证代币");
            console.log("");
          }
        } else {
          console.log(`ℹ️ ${nft.name} 跳过自动铸造许可证代币`);
          console.log("");
        }

      } catch (error) {
        console.error(`❌ ${nft.name} 处理失败:`, error);
        console.log("");
        continue;
      }

      // 添加延迟避免请求过快
      if (i < EXAMPLE_CONFIG.nfts.length - 1) {
        console.log("⏳ 等待 3 秒后处理下一个 NFT...");
        await new Promise(resolve => setTimeout(resolve, 3000));
        console.log("");
      }
    }

    // 显示总结
    console.log("🎉 所有 NFT 处理完成！");
    console.log("=" .repeat(60));
    console.log("");
    console.log("📋 处理总结:");
    EXAMPLE_CONFIG.nfts.forEach((nft, index) => {
      console.log(`${index + 1}. ${nft.name}`);
      if (nft.includeCommercial) {
        console.log("   ✅ 商业使用许可证 (10 WIP, 15% 分成, 可转让, 永久)");
      }
      if (nft.includeLimitedCommercial) {
        console.log("   ✅ 限量商业许可证 (50 WIP, 30% 分成, 不可转让, 1年, 1000 WIP 上限)");
      }
    });

    console.log("");
    console.log("💡 操作说明:");
    console.log("1. ✅ 许可证条款已成功添加到 IP 资产");
    console.log("2. 🪙 许可证代币已自动铸造给交易发起者（如果启用）");
    console.log("3. 💰 商业使用许可证: 10 WIP, 15% 分成, 可转让, 永久");
    console.log("4. 💎 限量商业许可证: 50 WIP, 30% 分成, 不可转让, 1年, 1000 WIP 上限");
    console.log("5. 🌐 在 Story Protocol Explorer 查看 IP 资产详情");
    console.log("");
    console.log("📝 代币分发规则:");
    console.log("• 许可证代币已发送到交易发起者的钱包地址");
    console.log("• 铸造费用从交易发起者钱包扣除");
    console.log("• 收益分成将在未来商业使用时分配给 IP 资产所有者");

  } catch (error) {
    console.error("💥 示例执行失败:", error);
  }
}

// 单独为一个 NFT 添加商业许可证的简化函数（支持自动铸造代币）
export async function addCommercialLicenseToSingleNFT(
  nftContract: Address,
  tokenId: string,
  includeCommercial: boolean = true,
  includeLimitedCommercial: boolean = false,
  useMainnet: boolean = false,
  options: {
    autoMintTokens?: boolean;
    mintCommercialTokens?: boolean;
    mintLimitedCommercialTokens?: boolean;
    tokenAmount?: number;
    receiver?: Address;
  } = {}
) {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  const {
    autoMintTokens = false,
    mintCommercialTokens = true,
    mintLimitedCommercialTokens = false,
    tokenAmount = 1,
    receiver
  } = options;

  const manager = new MainnetCommercialLicenseManager(
    process.env.WALLET_PRIVATE_KEY,
    useMainnet
  );

  // 添加许可证
  const result = await manager.addCommercialLicensesToNFT(
    nftContract,
    tokenId,
    includeCommercial,
    includeLimitedCommercial,
    false
  );

  // 自动铸造许可证代币（如果启用）
  if (autoMintTokens) {
    console.log("🪙 自动铸造许可证代币...");
    try {
      const mintResults = await manager.mintLicenseTokensForIP(
        result.ipId,
        {
          mintCommercial: mintCommercialTokens && includeCommercial,
          mintLimitedCommercial: mintLimitedCommercialTokens && includeLimitedCommercial,
          amount: tokenAmount,
          receiver
        }
      );

      return {
        ...result,
        mintResults
      };
    } catch (mintError) {
      console.log(`⚠️ 许可证代币铸造失败: ${mintError}`);
      return {
        ...result,
        mintError
      };
    }
  }

  return result;
}

// 批量处理多个 NFT 的函数（支持自动铸造代币）
export async function batchAddCommercialLicenses(
  nfts: Array<{
    contract: Address;
    tokenId: string;
    includeCommercial?: boolean;
    includeLimitedCommercial?: boolean;
    autoMintTokens?: boolean;
    mintCommercialTokens?: boolean;
    mintLimitedCommercialTokens?: boolean;
    tokenAmount?: number;
    receiver?: Address;
  }>,
  useMainnet: boolean = false
) {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  const manager = new MainnetCommercialLicenseManager(
    process.env.WALLET_PRIVATE_KEY,
    useMainnet
  );

  const results = [];

  for (const nft of nfts) {
    try {
      // 添加许可证
      const result = await manager.addCommercialLicensesToNFT(
        nft.contract,
        nft.tokenId,
        nft.includeCommercial ?? true,
        nft.includeLimitedCommercial ?? false,
        false
      );

      let mintResults = undefined;
      let mintError = undefined;

      // 自动铸造许可证代币（如果启用）
      if (nft.autoMintTokens) {
        try {
          mintResults = await manager.mintLicenseTokensForIP(
            result.ipId,
            {
              mintCommercial: (nft.mintCommercialTokens ?? true) && (nft.includeCommercial ?? true),
              mintLimitedCommercial: (nft.mintLimitedCommercialTokens ?? false) && (nft.includeLimitedCommercial ?? false),
              amount: nft.tokenAmount ?? 1,
              receiver: nft.receiver
            }
          );
        } catch (error) {
          mintError = error;
        }
      }

      results.push({
        success: true,
        nft,
        result: {
          ...result,
          mintResults,
          mintError
        }
      });
    } catch (error) {
      results.push({ success: false, nft, error });
    }
  }

  return results;
}

// 如果直接运行此脚本
if (require.main === module) {
  runCommercialLicenseExample().catch(console.error);
}
