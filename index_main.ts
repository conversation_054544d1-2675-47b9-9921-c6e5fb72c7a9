import { http } from "viem";
import { privateKeyToAccount, type Account, type Address } from "viem/accounts";
import { StoryClient, type StoryConfig } from "@story-protocol/core-sdk";
import { createHash } from "crypto";
import type { IpMetadata } from "@story-protocol/core-sdk";
import { zeroAddress } from "viem";

const privateKey: Address = `0x${process.env.WALLET_PRIVATE_KEY}`;
const account: Account = privateKeyToAccount(privateKey);

const config: StoryConfig = {
  account: account, // the account object from above
  transport: http(process.env.RPC_PROVIDER_URL),
  chainId: "aeneid",
  // chainId: "mainnet",
};
export const client = StoryClient.newClient(config);

import { PinataSDK } from "pinata-web3";

const pinata = new PinataSDK({
  pinataJwt: process.env.PINATA_JWT,
});

export async function uploadJSONToIPFS(jsonMetadata: any): Promise<string> {
  const { IpfsHash } = await pinata.upload.json(jsonMetadata);
  return IpfsHash;
}

async function main() {
  const ipMetadata: IpMetadata = client.ipAsset.generateIpMetadata({
    title: "DoubleFaceAngel",
    description: "I'm DFA.\nAlgorithms divide, emotions unite.",
    watermarkImg: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeidzowtbu74wdiibtzbrzc37fiufzqw7xbduv4c5venzws4335nquu",
  });

  const nftMetadata = {
    name: "DoubleFaceAngel",
    description: "I'm DFA.\nAlgorithms divide, emotions unite.",
    image: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeidzowtbu74wdiibtzbrzc37fiufzqw7xbduv4c5venzws4335nquu",
  };


  const ipMetadata2: IpMetadata = client.ipAsset.generateIpMetadata({
    title: "DoubleFaceAngel 2025",
    description: "I'm DFA 2025.\nAlgorithms divide, emotions unite.",
    watermarkImg: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeibhm4cowa6323bjaj3ffxfdb3zdgra6pm4f5dui63vtmkakoxyq7a",
  });

  const nftMetadata2 = {
    name: "DoubleFaceAngel 2025",
    description: "I'm DFA 2025.\nAlgorithms divide, emotions unite.",
    image: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeibhm4cowa6323bjaj3ffxfdb3zdgra6pm4f5dui63vtmkakoxyq7a",
  };

  const ipMetadata3: IpMetadata = client.ipAsset.generateIpMetadata({
    title: "DoubleFaceAngel 2075",
    description: "I'm DFA 2075.\nAlgorithms divide, emotions unite.",
    watermarkImg: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeic6mnsraqmgnobtykhtgvzmwknt6nak5ix3stym6h4e4c6qpu5k5i",
  });

  const nftMetadata3 = {
    name: "DoubleFaceAngel 2075",
    description: "I'm DFA 2075.\nAlgorithms divide, emotions unite.",
    image: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeic6mnsraqmgnobtykhtgvzmwknt6nak5ix3stym6h4e4c6qpu5k5i",
  };

  const ipIpfsHash = await uploadJSONToIPFS(ipMetadata);
  const ipHash = createHash("sha256")
    .update(JSON.stringify(ipMetadata))
    .digest("hex");

  const ipIpfsHash2 = await uploadJSONToIPFS(ipMetadata2);
  const ipHash2 = createHash("sha256")
    .update(JSON.stringify(ipMetadata2))
    .digest("hex");

  const ipIpfsHash3 = await uploadJSONToIPFS(ipMetadata3);
  const ipHash3 = createHash("sha256")
    .update(JSON.stringify(ipMetadata3))
    .digest("hex");

  const nftIpfsHash = await uploadJSONToIPFS(nftMetadata);
  const nftHash = createHash("sha256")
    .update(JSON.stringify(nftMetadata))
    .digest("hex");

  const nftIpfsHash2 = await uploadJSONToIPFS(nftMetadata2);
  const nftHash2 = createHash("sha256")
    .update(JSON.stringify(nftMetadata2))
    .digest("hex");

  const nftIpfsHash3 = await uploadJSONToIPFS(nftMetadata3);
  const nftHash3 = createHash("sha256")
    .update(JSON.stringify(nftMetadata3))
    .digest("hex");

  const newCollection = await client.nftClient.createNFTCollection({
    name: "DoubleFaceAngel",
    symbol: "DFA",
    isPublicMinting: false,
    mintOpen: true,
    mintFeeRecipient: zeroAddress,
    contractURI: "https://c6d09ecd8e2cfe7c90cc020049216015.ipfscdn.io/ipfs/bafybeidzowtbu74wdiibtzbrzc37fiufzqw7xbduv4c5venzws4335nquu",
    txOptions: { waitForTransaction: true },
  });

  console.log(
    `New SPG NFT collection created at transaction hash ${newCollection.txHash}`
  );
  console.log(`NFT contract address: ${newCollection.spgNftContract}`);

  const response = await client.ipAsset.mintAndRegisterIp({
    spgNftContract: newCollection.spgNftContract as Address,
    allowDuplicates: true,
    ipMetadata: {
      ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash}`,
      ipMetadataHash: `0x${ipHash}`,
      nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash}`,
      nftMetadataHash: `0x${nftHash}`,
    },
    txOptions: { waitForTransaction: true },
  });


  console.log(
    `Root IPA created at transaction hash ${response.txHash}, IPA ID: ${response.ipId}`
  );
  console.log(
    `View on the explorer: https://explorer.story.foundation/ipa/${response.ipId}`
  );

  const response2 = await client.ipAsset.mintAndRegisterIp({
    spgNftContract: newCollection.spgNftContract as Address,
    allowDuplicates: true,
    ipMetadata: {
      ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash2}`,
      ipMetadataHash: `0x${ipHash2}`,
      nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash2}`,
      nftMetadataHash: `0x${nftHash2}`,
    },
    txOptions: { waitForTransaction: true },
  });


  console.log(
    `Root IPA created at transaction hash ${response2.txHash}, IPA ID: ${response2.ipId}`
  );
  console.log(
    `View on the explorer: https://explorer.story.foundation/ipa/${response2.ipId}`
  );


  const response3 = await client.ipAsset.mintAndRegisterIp({
    spgNftContract: newCollection.spgNftContract as Address,
    allowDuplicates: true,
    ipMetadata: {
      ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash3}`,
      ipMetadataHash: `0x${ipHash3}`,
      nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash3}`,
      nftMetadataHash: `0x${nftHash3}`,
    },
    txOptions: { waitForTransaction: true },
  });


  console.log(
    `Root IPA created at transaction hash ${response3.txHash}, IPA ID: ${response3.ipId}`
  );
  console.log(
    `View on the explorer: https://explorer.story.foundation/ipa/${response3.ipId}`
  );

}

main();
