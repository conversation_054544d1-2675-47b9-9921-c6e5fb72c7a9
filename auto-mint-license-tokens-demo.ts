import { type Address } from "viem/accounts";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

/**
 * 自动铸造许可证代币演示
 * 演示如何在生成许可证之后自动为交易发起者铸造对应的许可代币
 */

// 示例配置 - 请根据实际情况修改
const AUTO_MINT_CONFIG = {
  // 网络设置
  useMainnet: false, // 设置为 true 使用主网

  // 示例 NFT 配置
  nft: {
    name: "自动铸造演示 NFT #001",
    contract: "0xfcf201CA09861aaF3597839Ac1B9B1aD17D40e81" as Address,
    tokenId: "1",
    
    // 许可证配置
    includeCommercial: true,
    includeLimitedCommercial: true,
    
    // 自动铸造配置
    autoMintTokens: true, // 启用自动铸造
    mintCommercialTokens: true, // 铸造商业使用许可证代币
    mintLimitedCommercialTokens: true, // 铸造限量商业许可证代币
    tokenAmount: 2, // 每种许可证代币铸造 2 个
    
    // 接收者配置（可选）
    receiver: undefined as Address | undefined, // 如果不指定，代币发送给交易发起者
  }
};

async function runAutoMintDemo() {
  console.log("🪙 自动铸造许可证代币演示");
  console.log("=" .repeat(60));
  console.log("本演示展示如何在生成许可证后自动铸造许可证代币给交易发起者");
  console.log("");

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  // 检查 NFT 配置
  if (AUTO_MINT_CONFIG.nft.contract.includes("你的NFT合约地址")) {
    console.log("⚠️ 请先配置真实的 NFT 合约地址");
    console.log("💡 修改 AUTO_MINT_CONFIG.nft.contract");
    return;
  }

  try {
    // 创建管理器实例
    console.log("🔧 初始化商业许可证管理器...");
    const manager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      AUTO_MINT_CONFIG.useMainnet
    );
    console.log(`✅ 管理器初始化完成 (${AUTO_MINT_CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log("");

    // 显示配置信息
    console.log("📋 演示配置:");
    console.log(`   NFT 名称: ${AUTO_MINT_CONFIG.nft.name}`);
    console.log(`   合约地址: ${AUTO_MINT_CONFIG.nft.contract}`);
    console.log(`   Token ID: ${AUTO_MINT_CONFIG.nft.tokenId}`);
    console.log(`   商业使用许可证: ${AUTO_MINT_CONFIG.nft.includeCommercial ? "✅" : "❌"}`);
    console.log(`   限量商业许可证: ${AUTO_MINT_CONFIG.nft.includeLimitedCommercial ? "✅" : "❌"}`);
    console.log(`   自动铸造代币: ${AUTO_MINT_CONFIG.nft.autoMintTokens ? "✅" : "❌"}`);
    console.log(`   铸造商业代币: ${AUTO_MINT_CONFIG.nft.mintCommercialTokens ? "✅" : "❌"}`);
    console.log(`   铸造限量代币: ${AUTO_MINT_CONFIG.nft.mintLimitedCommercialTokens ? "✅" : "❌"}`);
    console.log(`   每种代币数量: ${AUTO_MINT_CONFIG.nft.tokenAmount}`);
    console.log(`   接收者: ${AUTO_MINT_CONFIG.nft.receiver || "交易发起者钱包"}`);
    console.log("");

    // 步骤 1: 添加商业许可证
    console.log("🚀 步骤 1: 为 NFT 添加商业许可证");
    console.log("=" .repeat(50));
    
    const result = await manager.addCommercialLicensesToNFT(
      AUTO_MINT_CONFIG.nft.contract,
      AUTO_MINT_CONFIG.nft.tokenId,
      AUTO_MINT_CONFIG.nft.includeCommercial,
      AUTO_MINT_CONFIG.nft.includeLimitedCommercial,
      false
    );

    console.log(`✅ 许可证添加完成`);
    console.log(`   IP ID: ${result.ipId}`);
    if (result.commercialLicenseTermsId) {
      console.log(`   商业许可证 ID: ${result.commercialLicenseTermsId}`);
    }
    if (result.limitedCommercialLicenseTermsId) {
      console.log(`   限量商业许可证 ID: ${result.limitedCommercialLicenseTermsId}`);
    }
    console.log("");

    // 步骤 2: 自动铸造许可证代币
    if (AUTO_MINT_CONFIG.nft.autoMintTokens) {
      console.log("🪙 步骤 2: 自动铸造许可证代币给交易发起者");
      console.log("=" .repeat(50));
      
      try {
        const mintResults = await manager.mintLicenseTokensForIP(
          result.ipId,
          {
            mintCommercial: AUTO_MINT_CONFIG.nft.mintCommercialTokens && AUTO_MINT_CONFIG.nft.includeCommercial,
            mintLimitedCommercial: AUTO_MINT_CONFIG.nft.mintLimitedCommercialTokens && AUTO_MINT_CONFIG.nft.includeLimitedCommercial,
            amount: AUTO_MINT_CONFIG.nft.tokenAmount,
            receiver: AUTO_MINT_CONFIG.nft.receiver
          }
        );

        const successfulMints = mintResults.filter(r => r.success);
        const failedMints = mintResults.filter(r => !r.success);

        console.log(`✅ 许可证代币铸造完成`);
        console.log(`   成功铸造: ${successfulMints.length} 种许可证代币`);
        if (failedMints.length > 0) {
          console.log(`   失败铸造: ${failedMints.length} 种许可证代币`);
        }
        console.log("");

        // 显示详细的铸造结果
        console.log("📊 详细铸造结果:");
        console.log("=" .repeat(30));
        mintResults.forEach((mintResult, index) => {
          console.log(`${index + 1}. ${mintResult.type}:`);
          if (mintResult.success) {
            console.log(`   ✅ 成功 - 代币 IDs: ${mintResult.result.licenseTokenIds}`);
            console.log(`   📝 交易哈希: ${mintResult.result.txHash}`);
          } else {
            console.log(`   ❌ 失败 - 错误: ${mintResult.error}`);
          }
          console.log("");
        });

      } catch (mintError) {
        console.log(`⚠️ 许可证代币铸造失败: ${mintError}`);
        console.log("   许可证已成功添加，但代币铸造失败");
        console.log("   💡 可以稍后手动铸造许可证代币");
        console.log("");
      }
    } else {
      console.log("ℹ️ 步骤 2: 跳过自动铸造（未启用）");
      console.log("");
    }

    // 显示总结
    console.log("🎉 演示完成！");
    console.log("=" .repeat(60));
    console.log("");
    console.log("📋 操作总结:");
    console.log(`1. ✅ IP 资产已注册: ${result.ipId}`);
    console.log(`2. ✅ 许可证条款已附加到 IP 资产`);
    if (AUTO_MINT_CONFIG.nft.autoMintTokens) {
      console.log(`3. 🪙 许可证代币已自动铸造给交易发起者`);
      console.log(`   • 商业使用许可证代币: ${AUTO_MINT_CONFIG.nft.mintCommercialTokens ? AUTO_MINT_CONFIG.nft.tokenAmount + " 个" : "跳过"}`);
      console.log(`   • 限量商业许可证代币: ${AUTO_MINT_CONFIG.nft.mintLimitedCommercialTokens ? AUTO_MINT_CONFIG.nft.tokenAmount + " 个" : "跳过"}`);
    } else {
      console.log(`3. ℹ️ 许可证代币铸造已跳过（未启用自动铸造）`);
    }
    console.log("");
    console.log("💰 费用说明:");
    console.log("• 商业使用许可证代币: 10 WIP/个, 15% 收益分成, 可转让, 永久");
    console.log("• 限量商业许可证代币: 50 WIP/个, 30% 收益分成, 不可转让, 1年, 1000 WIP 上限");
    console.log("");
    console.log("🌐 查看详情:");
    console.log(`   Story Protocol Explorer: https://explorer.story.foundation/ipa/${result.ipId}`);

  } catch (error) {
    console.error("💥 演示执行失败:", error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAutoMintDemo().catch(console.error);
}

export { runAutoMintDemo, AUTO_MINT_CONFIG };
