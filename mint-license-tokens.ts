import { type Address } from "viem/accounts";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

/**
 * 铸造许可证代币示例
 * 演示如何为已有许可证条款的 IP 资产铸造许可证代币
 */

async function mintLicenseTokensExample() {
  console.log("🪙 许可证代币铸造示例");
  console.log("=" .repeat(60));

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    return;
  }

  // 配置信息 - 请根据实际情况修改
  const CONFIG = {
    // 已经注册并附加了许可证条款的 IP 资产
    ipId: "******************************************" as Address, // 替换为你的 IP ID

    // 铸造配置
    licenseType: "COMMERCIAL_USE", // 商业使用许可证
    amount: 1, // 铸造数量

    // 接收者地址（可选）
    receiver: undefined as Address | undefined, // 如果不指定，代币发送给发送交易的钱包

    useMainnet: false // 是否使用主网
  };

  try {
    // 创建管理器实例
    console.log("🔧 初始化许可证管理器...");
    const manager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      CONFIG.useMainnet
    );
    console.log(`✅ 管理器初始化完成 (${CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log("");

    console.log("📋 铸造配置:");
    console.log(`   IP 资产 ID: ${CONFIG.ipId}`);
    console.log(`   许可证类型: ${CONFIG.licenseType}`);
    console.log(`   铸造数量: ${CONFIG.amount}`);
    console.log(`   接收者: ${CONFIG.receiver || "发送交易的钱包地址"}`);
    console.log("");

    // 铸造许可证代币
    console.log("🪙 开始铸造许可证代币...");
    console.log("=" .repeat(50));

    // 注意：这里需要先确保商业许可证条款已经注册
    // 如果还没有注册，需要先调用 registerCommercialUseLicense()
    await manager.registerCommercialUseLicense();

    // 铸造代币的方法需要在 SDK 中调用
    // 由于当前的 MainnetCommercialLicenseManager 没有铸造方法，
    // 我们需要直接使用 client.license.mintLicenseTokens

    const licenseTermsId = manager.getCommercialLicenseTermsId();
    if (!licenseTermsId) {
      throw new Error("商业许可证条款未注册");
    }

    console.log(`使用许可证条款 ID: ${licenseTermsId}`);
    console.log("⚠️ 注意：商业许可证需要支付 10 WIP 的铸造费用");
    console.log("");

    // 这里展示如何调用铸造方法（需要在实际的 manager 中实现）
    console.log("💡 铸造许可证代币的方法调用:");
    console.log(`
    const response = await client.license.mintLicenseTokens({
      licenseTermsId: "${licenseTermsId}",
      licensorIpId: "${CONFIG.ipId}",
      amount: ${CONFIG.amount},
      receiver: ${CONFIG.receiver ? `"${CONFIG.receiver}"` : "undefined"}, // 可选
      maxMintingFee: parseEther("10"), // 10 WIP
      maxRevenueShare: 15, // 15% 收益分成
      txOptions: { waitForTransaction: true }
    });
    `);

    console.log("📝 代币分发规则:");
    console.log("1. 如果指定了 receiver 参数：代币发送给指定地址");
    console.log("2. 如果没有指定 receiver：代币发送给发送交易的钱包地址");
    console.log("3. 铸造费用从发送交易的钱包扣除");
    console.log("4. 收益分成会在未来商业使用时分配给 IP 资产所有者");

  } catch (error) {
    console.error("❌ 铸造失败:", error);
  }
}

// 运行示例
if (require.main === module) {
  mintLicenseTokensExample().catch(console.error);
}

export { mintLicenseTokensExample };
