# 自动铸造许可证代币功能说明

## 概述

现在系统支持在生成许可证之后自动为交易发起者铸造对应的许可代币。这个功能让用户在一次交易中完成许可证生成和代币铸造两个步骤。

## 主要功能

### 1. 自动铸造许可证代币
- 在为 NFT 添加商业许可证后，自动铸造对应的许可证代币
- 代币直接发送给交易发起者的钱包地址
- 支持批量处理多个 NFT

### 2. 支持的许可证类型
- **商业使用许可证代币**: 10 WIP/个, 15% 收益分成, 可转让, 永久有效
- **限量商业许可证代币**: 50 WIP/个, 30% 收益分成, 不可转让, 1年有效期, 1000 WIP 收益上限

## 使用方法

### 方法 1: 使用自动铸造演示脚本

```bash
# 运行自动铸造演示
npx ts-node auto-mint-license-tokens-demo.ts
```

配置文件中的关键参数：
```typescript
const AUTO_MINT_CONFIG = {
  nft: {
    // NFT 基本信息
    contract: "你的NFT合约地址",
    tokenId: "1",
    
    // 许可证配置
    includeCommercial: true,
    includeLimitedCommercial: true,
    
    // 自动铸造配置
    autoMintTokens: true, // 启用自动铸造
    mintCommercialTokens: true, // 铸造商业使用许可证代币
    mintLimitedCommercialTokens: true, // 铸造限量商业许可证代币
    tokenAmount: 2, // 每种许可证代币铸造数量
    
    // 可选：指定接收者地址
    receiver: undefined, // 不指定则发送给交易发起者
  }
};
```

### 方法 2: 使用更新后的 commercial-license-dfa2025.ts

```bash
# 运行更新后的商业许可证脚本
npx ts-node commercial-license-dfa2025.ts
```

在 `EXAMPLE_CONFIG` 中配置自动铸造：
```typescript
const EXAMPLE_CONFIG = {
  nfts: [
    {
      name: "测试艺术品 #001",
      contract: "你的NFT合约地址",
      tokenId: "1",
      includeCommercial: true,
      includeLimitedCommercial: true,
      
      // 新增的自动铸造配置
      autoMintTokens: true,
      mintCommercialTokens: true,
      mintLimitedCommercialTokens: true,
      tokenAmount: 1,
    },
  ]
};
```

### 方法 3: 编程方式调用

```typescript
import { addCommercialLicenseToSingleNFT } from "./commercial-license-dfa2025";

// 为单个 NFT 添加许可证并自动铸造代币
const result = await addCommercialLicenseToSingleNFT(
  "0xYourNFTContract",
  "1",
  true, // includeCommercial
  true, // includeLimitedCommercial
  false, // useMainnet
  {
    autoMintTokens: true,
    mintCommercialTokens: true,
    mintLimitedCommercialTokens: true,
    tokenAmount: 1,
    receiver: undefined // 发送给交易发起者
  }
);
```

## 代币分发规则

1. **默认接收者**: 如果不指定 `receiver` 参数，许可证代币将发送给发起交易的钱包地址
2. **指定接收者**: 可以通过 `receiver` 参数指定代币接收者地址
3. **费用扣除**: 铸造费用始终从发起交易的钱包扣除
4. **收益分成**: 未来的商业使用收益将分配给 IP 资产所有者

## 费用说明

### 商业使用许可证代币
- 铸造费用: 10 WIP/个
- 收益分成: 15%
- 可转让: 是
- 有效期: 永久

### 限量商业许可证代币
- 铸造费用: 50 WIP/个
- 收益分成: 30%
- 可转让: 否
- 有效期: 1年
- 收益上限: 1000 WIP

## 错误处理

系统会自动处理以下情况：
- **余额不足**: 如果钱包 WIP 余额不足，会显示友好的错误信息
- **许可证未注册**: 自动注册所需的许可证条款
- **部分失败**: 如果许可证添加成功但代币铸造失败，会显示相应提示

## 手动铸造代币

如果自动铸造失败或需要稍后铸造，可以使用：

```bash
# 手动铸造许可证代币
npx ts-node mint-license-tokens.ts
```

或者编程方式：
```typescript
import { mintLicenseTokensForIP } from "./mint-license-tokens";

// 手动铸造商业使用许可证代币
await mintLicenseTokensForIP(
  "0xYourIPId",
  "COMMERCIAL_USE",
  1, // 数量
  undefined, // 接收者（可选）
  false // useMainnet
);
```

## 注意事项

1. **环境配置**: 确保 `.env` 文件中配置了 `WALLET_PRIVATE_KEY`
2. **余额检查**: 铸造前确保钱包有足够的 WIP 代币余额
3. **网络选择**: 注意区分主网和测试网配置
4. **合约地址**: 使用真实的 NFT 合约地址替换示例地址

## 查看结果

铸造完成后，可以在以下地方查看结果：
- Story Protocol Explorer: `https://explorer.story.foundation/ipa/{ipId}`
- 钱包中查看许可证代币余额
- 交易哈希确认铸造状态
