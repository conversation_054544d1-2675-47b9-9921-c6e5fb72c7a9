// 网络配置文件
export const NETWORK_CONFIGS = {
  // 测试网配置 (Aeneid)
  testnet: {
    chainId: "aeneid",
    rpcUrl: "https://aeneid.storyrpc.io/",
    name: "Aeneid Testnet",
    explorer: "https://aeneid.explorer.story.foundation",
    contracts: {
      // 测试网合约地址
      royaltyPolicyLAP: "0xBe54FB168b3c982b7AaE60dB6CF75Bd8447b390E",
      wipToken: "0x1514000000000000000000000000000000000000",
      ipAssetRegistry: "0x77319B4031e6eF1250907aa00018B8B1c67a244b",
      licensingModule: "0x04fbd8a2e56dd85CFD5500A4A4DfA955B9f1dE6f",
      pilTemplate: "0x2E896b0b2Fdb7457499B56AAaA4AE55BCB4Cd316",
      licenseToken: "0xFe3838BFb30B34170F00030B52eA4893d8aAC6bC"
    }
  },

  // 主网配置 (Story Protocol Mainnet)
  mainnet: {
    chainId: "1516", // Story Protocol 主网链 ID
    rpcUrl: "https://rpc.story.foundation", // 主网 RPC
    name: "Story Protocol Mainnet",
    explorer: "https://explorer.story.foundation",
    contracts: {
      // ⚠️ 主网合约地址 - 需要根据实际部署更新
      royaltyPolicyLAP: "0x主网RoyaltyPolicyLAP地址",
      wipToken: "0x主网WIP代币地址",
      ipAssetRegistry: "0x主网IPAssetRegistry地址",
      licensingModule: "0x主网LicensingModule地址",
      pilTemplate: "0x主网PILTemplate地址",
      licenseToken: "0x主网LicenseToken地址"
    }
  }
};

// 获取网络配置
export function getNetworkConfig(useMainnet: boolean = false) {
  return useMainnet ? NETWORK_CONFIGS.mainnet : NETWORK_CONFIGS.testnet;
}

// 验证网络配置
export function validateNetworkConfig(config: typeof NETWORK_CONFIGS.mainnet) {
  const invalidAddresses = Object.entries(config.contracts).filter(
    ([key, address]) => address.includes("主网") || address.length !== 42
  );

  if (invalidAddresses.length > 0) {
    console.warn("⚠️ 检测到未配置的合约地址:");
    invalidAddresses.forEach(([key, address]) => {
      console.warn(`   ${key}: ${address}`);
    });
    return false;
  }

  return true;
}

// 显示网络信息
export function displayNetworkInfo(useMainnet: boolean = false) {
  const config = getNetworkConfig(useMainnet);
  
  console.log(`🌐 网络信息:`);
  console.log(`   名称: ${config.name}`);
  console.log(`   链 ID: ${config.chainId}`);
  console.log(`   RPC: ${config.rpcUrl}`);
  console.log(`   浏览器: ${config.explorer}`);
  
  if (useMainnet && !validateNetworkConfig(config)) {
    console.warn("⚠️ 主网配置不完整，请更新合约地址");
  }
}
