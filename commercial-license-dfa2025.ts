import { type Address } from "viem/accounts";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

/**
 * 商业许可证使用示例
 * 演示如何为不同的 NFT 添加商业使用和限量商业许可证
 */

// 示例配置 - 请根据实际情况修改
const EXAMPLE_CONFIG = {
  // 网络设置
  useMainnet: false, // 设置为 true 使用主网

  // 示例 NFT 列表
  nfts: [
    {
      name: "测试艺术品 #001",
      contract: "******************************************" as Address,
      tokenId: "1",
      includeCommercial: true,
      includeLimitedCommercial: true
    },
  ]
};

async function runCommercialLicenseExample() {
  console.log("🎨 商业许可证授权示例");
  console.log("=" .repeat(60));
  console.log("本示例演示如何为不同类型的 NFT 添加商业许可证");
  console.log("");

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  // 检查 NFT 配置
  const hasValidNFTs = EXAMPLE_CONFIG.nfts.some(nft =>
    !nft.contract.includes("你的NFT合约地址")
  );

  if (!hasValidNFTs) {
    console.log("⚠️ 请先配置真实的 NFT 合约地址");
    console.log("💡 修改 EXAMPLE_CONFIG.nfts 中的合约地址");
    console.log("");
    console.log("📋 当前配置的示例 NFT:");
    EXAMPLE_CONFIG.nfts.forEach((nft, index) => {
      console.log(`${index + 1}. ${nft.name}`);
      console.log(`   合约: ${nft.contract}`);
      console.log(`   Token ID: ${nft.tokenId}`);
      console.log(`   商业使用许可证: ${nft.includeCommercial ? "✅" : "❌"}`);
      console.log(`   限量商业许可证: ${nft.includeLimitedCommercial ? "✅" : "❌"}`);
      console.log("");
    });
    return;
  }

  try {
    // 创建管理器实例
    console.log("🔧 初始化商业许可证管理器...");
    const manager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      EXAMPLE_CONFIG.useMainnet
    );
    console.log(`✅ 管理器初始化完成 (${EXAMPLE_CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log("");

    // 处理每个 NFT
    for (let i = 0; i < EXAMPLE_CONFIG.nfts.length; i++) {
      const nft = EXAMPLE_CONFIG.nfts[i];

      console.log(`🖼️ 处理 NFT ${i + 1}/${EXAMPLE_CONFIG.nfts.length}: ${nft.name}`);
      console.log("=" .repeat(50));
      console.log(`合约地址: ${nft.contract}`);
      console.log(`Token ID: ${nft.tokenId}`);
      console.log(`商业使用许可证: ${nft.includeCommercial ? "包含" : "跳过"}`);
      console.log(`限量商业许可证: ${nft.includeLimitedCommercial ? "包含" : "跳过"}`);
      console.log("");

      try {
        // 为 NFT 添加商业许可证
        const result = await manager.addCommercialLicensesToNFT(
          nft.contract,
          nft.tokenId,
          nft.includeCommercial,
          nft.includeLimitedCommercial,
          false // 不强制注册新许可证
        );

        console.log(`✅ ${nft.name} 处理完成`);
        console.log(`   IP ID: ${result.ipId}`);
        if (result.commercialLicenseTermsId) {
          console.log(`   商业许可证 ID: ${result.commercialLicenseTermsId}`);
        }
        if (result.limitedCommercialLicenseTermsId) {
          console.log(`   限量商业许可证 ID: ${result.limitedCommercialLicenseTermsId}`);
        }
        console.log(`   新注册的 IP: ${result.isNewIP ? "是" : "否"}`);
        console.log("");

      } catch (error) {
        console.error(`❌ ${nft.name} 处理失败:`, error);
        console.log("");
        continue;
      }

      // 添加延迟避免请求过快
      if (i < EXAMPLE_CONFIG.nfts.length - 1) {
        console.log("⏳ 等待 3 秒后处理下一个 NFT...");
        await new Promise(resolve => setTimeout(resolve, 3000));
        console.log("");
      }
    }

    // 显示总结
    console.log("🎉 所有 NFT 处理完成！");
    console.log("=" .repeat(60));
    console.log("");
    console.log("📋 处理总结:");
    EXAMPLE_CONFIG.nfts.forEach((nft, index) => {
      console.log(`${index + 1}. ${nft.name}`);
      if (nft.includeCommercial) {
        console.log("   ✅ 商业使用许可证 (10 WIP, 15% 分成, 可转让, 永久)");
      }
      if (nft.includeLimitedCommercial) {
        console.log("   ✅ 限量商业许可证 (50 WIP, 30% 分成, 不可转让, 1年, 1000 WIP 上限)");
      }
    });

    console.log("");
    console.log("💡 下一步操作:");
    console.log("1. 用户可以通过支付相应费用铸造许可证代币");
    console.log("2. 商业使用许可证允许商业用途，可转让");
    console.log("3. 限量商业许可证提供更高收益分成，但有时间和收益限制");
    console.log("4. 在 Story Protocol Explorer 查看 IP 资产详情");

  } catch (error) {
    console.error("💥 示例执行失败:", error);
  }
}

// 单独为一个 NFT 添加商业许可证的简化函数
export async function addCommercialLicenseToSingleNFT(
  nftContract: Address,
  tokenId: string,
  includeCommercial: boolean = true,
  includeLimitedCommercial: boolean = false,
  useMainnet: boolean = false
) {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  const manager = new MainnetCommercialLicenseManager(
    process.env.WALLET_PRIVATE_KEY,
    useMainnet
  );

  return await manager.addCommercialLicensesToNFT(
    nftContract,
    tokenId,
    includeCommercial,
    includeLimitedCommercial,
    false
  );
}

// 批量处理多个 NFT 的函数
export async function batchAddCommercialLicenses(
  nfts: Array<{
    contract: Address;
    tokenId: string;
    includeCommercial?: boolean;
    includeLimitedCommercial?: boolean;
  }>,
  useMainnet: boolean = false
) {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  const manager = new MainnetCommercialLicenseManager(
    process.env.WALLET_PRIVATE_KEY,
    useMainnet
  );

  const results = [];

  for (const nft of nfts) {
    try {
      const result = await manager.addCommercialLicensesToNFT(
        nft.contract,
        nft.tokenId,
        nft.includeCommercial ?? true,
        nft.includeLimitedCommercial ?? false,
        false
      );
      results.push({ success: true, nft, result });
    } catch (error) {
      results.push({ success: false, nft, error });
    }
  }

  return results;
}

// 如果直接运行此脚本
if (require.main === module) {
  runCommercialLicenseExample().catch(console.error);
}
