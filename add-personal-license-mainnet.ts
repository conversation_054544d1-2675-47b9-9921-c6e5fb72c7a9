import { http } from "viem";
import { privateKeyToAccount, type Account, type Address } from "viem/accounts";
import { StoryClient, type StoryConfig, type LicenseTerms } from "@story-protocol/core-sdk";
import { zeroAddress } from "viem";

// 主网配置
const MAINNET_CONFIG = {
  chainId: "1516", // Story Protocol 主网链 ID
  rpcUrl: "https://rpc.story.foundation", // 主网 RPC
  // 主网合约地址 (需要根据实际部署地址更新)
  contracts: {
    royaltyPolicyLAP: "0x主网RoyaltyPolicyLAP地址",
    wipToken: "0x主网WIP代币地址"
  }
};

// 个人使用许可证配置 (免费基础权限碎片)
const PERSONAL_USE_LICENSE: LicenseTerms = {
  transferable: false,
  royaltyPolicy: zeroAddress, // 非商业使用不需要版税政策
  defaultMintingFee: 0n, // 免费
  expiration: 0n, // 永不过期
  commercialUse: false, // 不允许商业使用
  commercialAttribution: false, // 非商业使用时不需要商业署名
  commercializerChecker: zeroAddress,
  commercializerCheckerData: "0x",
  commercialRevShare: 0, // 无收益分成
  commercialRevCeiling: 0n,
  derivativesAllowed: false, // 不允许衍生作品
  derivativesAttribution: false, // 不需要衍生署名
  derivativesApproval: false,
  derivativesReciprocal: false,
  derivativeRevCeiling: 0n,
  currency: zeroAddress, // 免费许可证不需要货币
  uri: "https://github.com/piplabs/pil-document/blob/main/off-chain-terms/PersonalUse.json"
};

// 为指定 NFT 添加个人使用许可证的类
export class MainnetPersonalLicenseManager {
  private client: StoryClient;
  private personalLicenseTermsId?: bigint;
  private chainId: bigint;

  constructor(privateKey: string, useMainnet: boolean = false) {
    const account: Account = privateKeyToAccount(`0x${privateKey}` as Address);

    const config: StoryConfig = {
      account: account,
      transport: http(useMainnet ? MAINNET_CONFIG.rpcUrl : process.env.RPC_PROVIDER_URL),
      chainId: useMainnet ? "mainnet" : "aeneid",
    };

    this.client = StoryClient.newClient(config);
    this.chainId = useMainnet ? BigInt(1516) : BigInt(1513); // 主网: 1516, 测试网: 1513
  }

  // 注册个人使用许可证条款
  async registerPersonalUseLicense(): Promise<bigint> {
    console.log("🔄 注册个人使用许可证条款...");

    try {
      const response = await this.client.license.registerPILTerms({
        ...PERSONAL_USE_LICENSE,
        txOptions: { waitForTransaction: true }
      });

      if (response.licenseTermsId) {
        this.personalLicenseTermsId = response.licenseTermsId;
        console.log(`✅ 个人使用许可证条款已注册`);
        console.log(`   许可证条款 ID: ${response.licenseTermsId}`);
        console.log(`   交易哈希: ${response.txHash}`);
        return response.licenseTermsId;
      } else {
        throw new Error("注册许可证条款失败：未返回许可证条款 ID");
      }
    } catch (error) {
      console.error("❌ 注册个人使用许可证条款失败:", error);
      throw error;
    }
  }

  // 检查 NFT 是否已注册为 IP 资产
  async checkIfNFTIsRegisteredAsIP(nftContract: Address, tokenId: string): Promise<Address | null> {
    try {
      console.log(`🔍 检查 NFT 是否已注册为 IP 资产...`);
      console.log(`   NFT 合约: ${nftContract}`);
      console.log(`   Token ID: ${tokenId}`);

      // 计算预期的 IP ID
      const ipId = await this.client.ipAsset.ipAssetRegistryClient.ipId({
        chainId: this.chainId,
        tokenContract: nftContract,
        tokenId: BigInt(tokenId)
      });

      if (ipId && ipId !== zeroAddress) {
        // 验证 IP 是否真正注册
        const isRegistered = await this.client.ipAsset.isRegistered(ipId);

        if (isRegistered) {
          console.log(`✅ NFT 已注册为 IP 资产`);
          console.log(`   IP ID: ${ipId}`);
          return ipId;
        } else {
          console.log(`ℹ️ NFT 尚未注册为 IP 资产 (计算出的 IP ID 未注册)`);
          return null;
        }
      } else {
        console.log(`ℹ️ NFT 尚未注册为 IP 资产 (无效的 IP ID)`);
        return null;
      }
    } catch (error) {
      console.log(`ℹ️ NFT 尚未注册为 IP 资产 (检查失败):`, error);
      return null;
    }
  }

  // 将 NFT 注册为 IP 资产
  async registerNFTAsIP(nftContract: Address, tokenId: string): Promise<Address> {
    console.log(`📝 将 NFT 注册为 IP 资产...`);

    try {
      const response = await this.client.ipAsset.register({
        nftContract,
        tokenId,
        txOptions: { waitForTransaction: true }
      });

      if (response.ipId) {
        console.log(`✅ NFT 已成功注册为 IP 资产`);
        console.log(`   IP ID: ${response.ipId}`);
        console.log(`   交易哈希: ${response.txHash}`);
        return response.ipId;
      } else {
        throw new Error("注册 IP 资产失败：未返回 IP ID");
      }
    } catch (error) {
      console.error("❌ 注册 IP 资产失败:", error);
      throw error;
    }
  }

  // 为 IP 资产附加个人使用许可证
  async attachPersonalLicenseToIP(ipId: Address): Promise<void> {
    if (!this.personalLicenseTermsId) {
      throw new Error("请先注册个人使用许可证条款");
    }

    console.log(`🔗 为 IP 资产附加个人使用许可证...`);
    console.log(`   IP ID: ${ipId}`);
    console.log(`   许可证条款 ID: ${this.personalLicenseTermsId}`);

    try {
      const response = await this.client.license.attachLicenseTerms({
        licenseTermsId: this.personalLicenseTermsId.toString(),
        ipId,
        txOptions: { waitForTransaction: true },
      });

      if (response.success) {
        console.log(`✅ 个人使用许可证已成功附加到 IP 资产`);
        console.log(`   交易哈希: ${response.txHash}`);
      } else {
        console.log(`ℹ️ 个人使用许可证可能已经附加到此 IP 资产`);
      }
    } catch (error) {
      console.error("❌ 附加许可证失败:", error);
      throw error;
    }
  }

  // 完整流程：为指定 NFT 添加个人使用许可证
  async addPersonalLicenseToNFT(
    nftContract: Address,
    tokenId: string,
    forceRegisterNewLicense: boolean = false
  ): Promise<{
    ipId: Address;
    licenseTermsId: bigint;
    isNewIP: boolean;
  }> {
    console.log("🚀 开始为 NFT 添加个人使用许可证...");
    console.log("=" .repeat(60));
    console.log(`NFT 合约地址: ${nftContract}`);
    console.log(`Token ID: ${tokenId}`);
    console.log("");

    try {
      // 步骤 1: 注册或获取个人使用许可证条款
      let licenseTermsId: bigint;
      if (forceRegisterNewLicense || !this.personalLicenseTermsId) {
        licenseTermsId = await this.registerPersonalUseLicense();
      } else {
        licenseTermsId = this.personalLicenseTermsId;
        console.log(`ℹ️ 使用已有的许可证条款 ID: ${licenseTermsId}`);
      }
      console.log("");

      // 步骤 2: 检查 NFT 是否已注册为 IP
      let ipId = await this.checkIfNFTIsRegisteredAsIP(nftContract, tokenId);
      let isNewIP = false;
      console.log("");

      // 步骤 3: 如果未注册，则注册为 IP 资产
      if (!ipId) {
        ipId = await this.registerNFTAsIP(nftContract, tokenId);
        isNewIP = true;
        console.log("");
      }

      // 步骤 4: 附加个人使用许可证
      await this.attachPersonalLicenseToIP(ipId);
      console.log("");

      // 步骤 5: 显示结果
      console.log("🎉 个人使用许可证添加完成！");
      console.log("=" .repeat(60));
      console.log(`✅ IP 资产 ID: ${ipId}`);
      console.log(`✅ 许可证条款 ID: ${licenseTermsId}`);
      console.log(`✅ 是否为新注册的 IP: ${isNewIP ? "是" : "否"}`);
      console.log("");
      console.log("📋 许可证详情:");
      console.log("   • 类型: 个人使用许可证 (基础权限碎片)");
      console.log("   • 费用: 免费");
      console.log("   • 商业使用: ❌ 不允许");
      console.log("   • 衍生作品: ❌ 不允许");
      console.log("   • 转让性: ❌ 不可转让");
      console.log("   • 有效期: ♾️ 永久有效");
      console.log("");
      console.log("🌐 浏览器查看:");
      console.log(`   https://explorer.story.foundation/ipa/${ipId}`);

      return {
        ipId,
        licenseTermsId,
        isNewIP
      };

    } catch (error) {
      console.error("💥 添加个人使用许可证失败:", error);
      throw error;
    }
  }

  // 获取许可证条款 ID
  getPersonalLicenseTermsId(): bigint | undefined {
    return this.personalLicenseTermsId;
  }
}

// 主函数 - 使用示例
async function main() {
  // 配置参数 - 请根据实际情况修改
  const CONFIG = {
    // 是否使用主网 (true = 主网, false = 测试网)
    useMainnet: false, // ⚠️ 设置为 true 以使用主网

    // NFT 信息
    nftContract: "0x你的NFT合约地址" as Address, // 替换为实际的 NFT 合约地址
    tokenId: "1", // 替换为实际的 Token ID

    // 是否强制注册新的许可证条款
    forceRegisterNewLicense: false
  };

  // 验证配置
  if (CONFIG.nftContract === "0x你的NFT合约地址") {
    console.error("❌ 请先配置正确的 NFT 合约地址");
    process.exit(1);
  }

  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    process.exit(1);
  }

  try {
    // 创建管理器实例
    const manager = new MainnetPersonalLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      CONFIG.useMainnet
    );

    // 执行添加许可证流程
    const result = await manager.addPersonalLicenseToNFT(
      CONFIG.nftContract,
      CONFIG.tokenId,
      CONFIG.forceRegisterNewLicense
    );

    console.log("🎯 操作成功完成！");

  } catch (error) {
    console.error("💥 操作失败:", error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

// 导出配置
export { PERSONAL_USE_LICENSE };
