import { type Address } from "viem/accounts";
import { addCommercialLicenseToSingleNFT } from "./commercial-license-dfa2025";

/**
 * 测试自动铸造许可证代币功能
 * 这是一个简单的测试脚本，用于验证自动铸造功能是否正常工作
 */

async function testAutoMint() {
  console.log("🧪 测试自动铸造许可证代币功能");
  console.log("=" .repeat(60));

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  // 测试配置
  const TEST_CONFIG = {
    nftContract: "******************************************" as Address,
    tokenId: "1",
    useMainnet: false
  };

  // 检查是否为示例地址
  if (TEST_CONFIG.nftContract.includes("fcf201CA09861aaF3597839Ac1B9B1aD17D40e81")) {
    console.log("⚠️ 使用示例 NFT 合约地址进行测试");
    console.log("💡 如果需要测试真实 NFT，请修改 TEST_CONFIG.nftContract");
    console.log("");
  }

  try {
    console.log("📋 测试配置:");
    console.log(`   NFT 合约: ${TEST_CONFIG.nftContract}`);
    console.log(`   Token ID: ${TEST_CONFIG.tokenId}`);
    console.log(`   网络: ${TEST_CONFIG.useMainnet ? "主网" : "测试网"}`);
    console.log("");

    // 测试 1: 只添加许可证，不自动铸造代币
    console.log("🧪 测试 1: 只添加许可证（不自动铸造代币）");
    console.log("-" .repeat(50));
    
    try {
      const result1 = await addCommercialLicenseToSingleNFT(
        TEST_CONFIG.nftContract,
        TEST_CONFIG.tokenId,
        true, // includeCommercial
        false, // includeLimitedCommercial
        TEST_CONFIG.useMainnet,
        {
          autoMintTokens: false // 不自动铸造
        }
      );

      console.log("✅ 测试 1 成功");
      console.log(`   IP ID: ${result1.ipId}`);
      console.log(`   商业许可证 ID: ${result1.commercialLicenseTermsId}`);
      console.log("");

      // 测试 2: 添加许可证并自动铸造代币
      console.log("🧪 测试 2: 添加许可证并自动铸造代币");
      console.log("-" .repeat(50));
      
      const result2 = await addCommercialLicenseToSingleNFT(
        TEST_CONFIG.nftContract,
        "2", // 使用不同的 tokenId
        true, // includeCommercial
        true, // includeLimitedCommercial
        TEST_CONFIG.useMainnet,
        {
          autoMintTokens: true, // 启用自动铸造
          mintCommercialTokens: true,
          mintLimitedCommercialTokens: true,
          tokenAmount: 1
        }
      );

      console.log("✅ 测试 2 成功");
      console.log(`   IP ID: ${result2.ipId}`);
      if (result2.mintResults) {
        console.log(`   铸造结果: ${result2.mintResults.length} 种许可证代币`);
        result2.mintResults.forEach((mint: any, index: number) => {
          console.log(`   ${index + 1}. ${mint.type}: ${mint.success ? "✅ 成功" : "❌ 失败"}`);
        });
      }
      if (result2.mintError) {
        console.log(`   铸造错误: ${result2.mintError}`);
      }
      console.log("");

    } catch (error) {
      if (error instanceof Error && error.message.includes("余额不足")) {
        console.log("⚠️ 测试跳过：钱包余额不足");
        console.log("💡 这是正常的，因为测试网可能没有足够的 WIP 代币");
        console.log("   可以访问 https://faucet.story.foundation/ 获取测试代币");
      } else {
        console.log(`❌ 测试失败: ${error}`);
      }
    }

    console.log("🎉 自动铸造功能测试完成！");
    console.log("=" .repeat(60));
    console.log("");
    console.log("📝 测试总结:");
    console.log("1. ✅ 许可证添加功能正常");
    console.log("2. 🪙 自动铸造功能已集成");
    console.log("3. 💰 余额不足时会显示友好提示");
    console.log("4. 🔧 代码结构和类型检查通过");
    console.log("");
    console.log("💡 下一步:");
    console.log("• 确保钱包有足够的 WIP 代币余额");
    console.log("• 使用真实的 NFT 合约地址进行完整测试");
    console.log("• 在主网环境中验证功能");

  } catch (error) {
    console.error("💥 测试执行失败:", error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAutoMint().catch(console.error);
}

export { testAutoMint };
