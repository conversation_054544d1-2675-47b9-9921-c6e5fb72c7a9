import { type Address } from "viem/accounts";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

/**
 * 测试脚本：检查 NFT 是否已注册为 IP 资产
 */

async function testNFTRegistration() {
  console.log("🔍 测试 NFT 注册状态");
  console.log("=" .repeat(50));

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    return;
  }

  // 测试的 NFT 信息
  const testNFT = {
    contract: "******************************************" as Address,
    tokenId: "1"
  };

  try {
    // 创建管理器实例
    const manager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      false // 使用测试网
    );

    console.log(`测试 NFT: ${testNFT.contract}`);
    console.log(`Token ID: ${testNFT.tokenId}`);
    console.log("");

    // 检查 NFT 是否已注册为 IP 资产
    const ipId = await manager.checkIfNFTIsRegisteredAsIP(
      testNFT.contract,
      testNFT.tokenId
    );

    if (ipId) {
      console.log("✅ 结果: NFT 已注册为 IP 资产");
      console.log(`IP ID: ${ipId}`);
      console.log("");
      console.log("💡 可以继续添加许可证");
    } else {
      console.log("❌ 结果: NFT 尚未注册为 IP 资产");
      console.log("");
      console.log("💡 需要先注册 NFT 为 IP 资产，然后才能添加许可证");
      console.log("建议操作:");
      console.log("1. 确认 NFT 合约地址和 Token ID 正确");
      console.log("2. 确认 NFT 存在且属于当前钱包");
      console.log("3. 先注册 NFT 为 IP 资产");
    }

  } catch (error) {
    console.error("❌ 测试失败:", error);
  }
}

// 运行测试
if (require.main === module) {
  testNFTRegistration().catch(console.error);
}

export { testNFTRegistration };
