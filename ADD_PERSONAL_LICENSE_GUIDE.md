# 为现有 NFT 添加个人使用许可证指南

## 🎯 功能说明

这个脚本可以为主网或测试网上已部署的 NFT 集合中的指定 NFT 添加【个人使用，基础权限碎片】许可证。

## 📋 个人使用许可证特性

- **💰 费用**: 完全免费
- **🏢 商业使用**: ❌ 不允许
- **🎨 衍生作品**: ❌ 不允许  
- **🔄 可转让**: ❌ 不可转让
- **⏰ 有效期**: ♾️ 永久有效
- **🎯 适用场景**: 个人收藏、非商业分享、学习研究

## 🚀 快速开始

### 1. 环境准备

确保你的 `.env` 文件包含必要的配置：

```bash
# 钱包私钥 (不包含 0x 前缀)
WALLET_PRIVATE_KEY=你的私钥

# RPC 提供商 URL (测试网)
RPC_PROVIDER_URL=https://aeneid.storyrpc.io/

# Pinata JWT (用于 IPFS 上传)
PINATA_JWT=你的Pinata_JWT令牌
```

### 2. 配置 NFT 信息

编辑 `quick-add-personal-license.ts` 文件顶部的配置：

```typescript
const NFT_CONFIG = {
  // 网络选择
  useMainnet: false, // true = 主网, false = 测试网
  
  // NFT 信息
  nftContract: "0x你的NFT合约地址" as Address,
  tokenId: "1", // 你的 Token ID
  
  // 高级选项
  forceRegisterNewLicense: false,
};
```

### 3. 运行脚本

```bash
# 快速添加个人使用许可证
bun run add-personal-license

# 或者直接运行
bun run quick-add-personal-license.ts
```

## 📖 详细使用步骤

### 步骤 1: 配置网络

#### 测试网 (推荐先测试)
```typescript
useMainnet: false
```

#### 主网 (生产环境)
```typescript
useMainnet: true
```

⚠️ **主网注意事项**:
- 需要真实的 gas 费用
- 确保钱包有足够的 ETH/IP 余额
- 主网合约地址需要在 `network-config.ts` 中正确配置

### 步骤 2: 配置 NFT 信息

```typescript
const NFT_CONFIG = {
  nftContract: "0xYourNFTContractAddress", // 42 字符地址
  tokenId: "123", // NFT 的 Token ID
};
```

### 步骤 3: 执行脚本

脚本会自动执行以下流程：

1. **验证配置** - 检查所有必要参数
2. **检查 IP 状态** - 确认 NFT 是否已注册为 IP 资产
3. **注册许可证条款** - 创建个人使用许可证条款
4. **注册 IP 资产** - 如果需要，将 NFT 注册为 IP 资产
5. **附加许可证** - 将个人使用许可证附加到 IP 资产

## 🔧 高级配置

### 强制注册新许可证

如果你想注册新的许可证条款而不是复用现有的：

```typescript
forceRegisterNewLicense: true
```

### 主网合约地址配置

编辑 `network-config.ts` 文件，更新主网合约地址：

```typescript
mainnet: {
  contracts: {
    royaltyPolicyLAP: "0x实际的主网地址",
    wipToken: "0x实际的主网地址",
    // ... 其他合约地址
  }
}
```

## 📊 脚本输出示例

```
🚀 快速添加个人使用许可证工具
============================================================

🔧 当前配置:
==================================================
网络: 测试网
NFT 合约: 0x6b1B002c95AD7d270D9Cb9C30de45fc31A81CedE
Token ID: 1
强制注册新许可证: 否

🌐 网络信息:
   名称: Aeneid Testnet
   链 ID: aeneid
   RPC: https://aeneid.storyrpc.io/
   浏览器: https://aeneid.explorer.story.foundation

🔄 注册个人使用许可证条款...
✅ 个人使用许可证条款已注册
   许可证条款 ID: 1234
   交易哈希: 0x...

🔍 检查 NFT 是否已注册为 IP 资产...
✅ NFT 已注册为 IP 资产
   IP ID: 0x...

🔗 为 IP 资产附加个人使用许可证...
✅ 个人使用许可证已成功附加到 IP 资产

🎉 个人使用许可证添加完成！
============================================================
✅ IP 资产 ID: 0x...
✅ 许可证条款 ID: 1234
✅ 是否为新注册的 IP: 否

🔗 相关链接:
   IP 资产浏览器: https://aeneid.explorer.story.foundation/ipa/0x...
```

## ⚠️ 注意事项

### 1. 网络选择
- **测试网**: 免费，适合测试和学习
- **主网**: 需要真实费用，用于生产环境

### 2. 权限要求
- 必须是 NFT 的所有者或被授权的操作者
- 钱包需要有足够的 gas 费用

### 3. 合约地址
- 确保 NFT 合约地址正确 (42 字符，以 0x 开头)
- Token ID 必须存在且有效

### 4. 主网配置
- 主网合约地址需要正确配置
- 建议先在测试网验证流程

## 🔍 故障排除

### 常见错误及解决方案

1. **配置错误**
   ```
   ❌ 请配置正确的 NFT 合约地址
   ```
   - 检查合约地址格式 (42 字符)
   - 确认地址正确

2. **余额不足**
   ```
   ❌ 余额不足
   ```
   - 确保钱包有足够的 gas 费用
   - 测试网可从水龙头获取代币

3. **权限不足**
   ```
   ❌ 权限不足
   ```
   - 确认是 NFT 所有者
   - 检查钱包私钥正确

4. **网络连接**
   ```
   ❌ 网络连接失败
   ```
   - 检查 RPC URL 配置
   - 确认网络连接正常

## 📚 相关文档

- [Story Protocol 官方文档](https://docs.story.foundation/)
- [单张形象授权详细文档](./SINGLE_IMAGE_LICENSING.md)
- [碎片化授权概念说明](./IMPLEMENTATION_SUMMARY.md)

## 🆘 获取帮助

如果遇到问题：

1. 检查配置是否正确
2. 查看控制台错误信息
3. 确认网络和合约地址
4. 验证钱包权限和余额

## 🎉 成功后的下一步

添加个人使用许可证后，你可以：

1. **铸造许可证代币** - 让用户免费获取使用权
2. **添加其他许可证** - 商业使用、衍生作品等
3. **设置收益分成** - 为付费许可证配置分成比例
4. **推广 IP 资产** - 利用免费许可证扩大影响力
