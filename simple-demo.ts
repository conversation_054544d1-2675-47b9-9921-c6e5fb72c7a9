import { client } from "./single-image-licensing";
import { SingleImageLicenseManager } from "./single-image-licensing";
import { zeroAddress } from "viem";
import type { Address } from "viem/accounts";

async function simpleDemo() {
  console.log("🚀 简化演示：单张形象授权功能（仅免费功能）\n");

  const licenseManager = new SingleImageLicenseManager(client);

  try {
    // 步骤 1: 检查余额建议
    console.log("💰 步骤 1: 余额检查");
    console.log("=" .repeat(50));
    await licenseManager.checkWalletBalance();
    console.log("\n");

    // 步骤 2: 注册许可证条款
    console.log("📋 步骤 2: 注册许可证条款");
    console.log("=" .repeat(50));
    await licenseManager.registerAllLicenseTerms();
    console.log("\n");

    // 步骤 3: 创建 NFT 集合
    console.log("🎨 步骤 3: 创建 NFT 集合");
    console.log("=" .repeat(50));
    const collection = await client.nftClient.createNFTCollection({
      name: "Simple Demo Collection",
      symbol: "SDC",
      isPublicMinting: false,
      mintOpen: true,
      mintFeeRecipient: zeroAddress,
      contractURI: "https://example.com/collection.json",
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ NFT 集合创建成功: ${collection.spgNftContract}\n`);

    // 步骤 4: 创建仅支持个人使用的图像 IP
    console.log("🖼️ 步骤 4: 创建个人使用图像 IP");
    console.log("=" .repeat(50));
    const personalImage = await licenseManager.createSingleImageIPWithLicensing(
      {
        title: "个人使用图像",
        description: "这是一个仅支持个人使用的图像",
        imageUrl: "https://picsum.photos/id/100/400/300"
      },
      collection.spgNftContract as Address,
      ["PERSONAL_USE"] // 仅个人使用，免费
    );
    console.log("\n");

    // 步骤 5: 铸造免费的个人使用许可证代币
    console.log("🪙 步骤 5: 铸造免费许可证代币");
    console.log("=" .repeat(50));
    if (personalImage.ipId) {
      try {
        await licenseManager.safeMintLicenseToken(
          personalImage.ipId,
          "PERSONAL_USE",
          1
        );
      } catch (error) {
        console.log("铸造过程中遇到问题，但这不影响核心功能演示");
      }
    }
    console.log("\n");

    // 步骤 6: 展示付费许可证的配置（不实际铸造）
    console.log("💎 步骤 6: 付费许可证配置展示");
    console.log("=" .repeat(50));
    console.log("以下是付费许可证的配置（需要足够余额才能铸造）:");
    console.log("");
    console.log("商业使用许可证:");
    console.log("  • 费用: 10 $WIP");
    console.log("  • 用途: 商业使用");
    console.log("  • 收益分成: 15%");
    console.log("");
    console.log("衍生作品许可证:");
    console.log("  • 费用: 5 $WIP");
    console.log("  • 用途: 创建衍生作品");
    console.log("  • 收益分成: 25%");
    console.log("");
    console.log("限量商业许可证:");
    console.log("  • 费用: 50 $WIP");
    console.log("  • 用途: 高级商业使用");
    console.log("  • 有效期: 1年");
    console.log("  • 收益分成: 30%");
    console.log("\n");

    // 步骤 7: 总结
    console.log("📊 步骤 7: 演示总结");
    console.log("=" .repeat(50));
    console.log("✅ 成功完成的功能:");
    console.log("   • 许可证条款注册");
    console.log("   • NFT 集合创建");
    console.log("   • 个人使用 IP 资产创建");
    console.log("   • 免费许可证代币铸造");
    console.log("");
    console.log("💡 要体验付费功能，请:");
    console.log("   1. 访问 https://faucet.story.foundation/ 获取测试代币");
    console.log("   2. 确保钱包有足够的 $WIP 余额");
    console.log("   3. 运行 'bun run single-image-demo' 体验完整功能");
    console.log("");
    console.log("🎯 创建的 IP 资产:");
    if (personalImage.ipId) {
      console.log(`   IP ID: ${personalImage.ipId}`);
      console.log(`   浏览器: https://aeneid.explorer.story.foundation/ipa/${personalImage.ipId}`);
      console.log(`   支持的许可证: 个人使用（免费）`);
    }

    console.log("\n🎉 简化演示完成！核心功能正常工作。");

  } catch (error) {
    console.error("❌ 演示过程中发生错误:", error);
    console.log("\n💡 如果遇到网络问题，请检查:");
    console.log("   • 网络连接是否正常");
    console.log("   • RPC_PROVIDER_URL 是否正确");
    console.log("   • PINATA_JWT 是否有效");
  }
}

// 运行简化演示
simpleDemo();
