# 商业许可证授权脚本使用指南

## 概述

本脚本 `add-commercial-license-mainnet.ts` 用于为指定 collection 的指定 NFT 授权商业使用碎片和限量商业碎片。基于现有的 personal license 脚本架构，提供了完整的商业许可证管理功能。

## 功能特性

### 🏢 商业使用许可证 (Commercial Use Fragment)
- **费用**: 10 $WIP
- **商业使用**: ✅ 允许
- **衍生作品**: ❌ 不允许  
- **转让性**: ✅ 可转让
- **收益分成**: 15%
- **有效期**: ♾️ 永久有效
- **收益上限**: 无限制

### 💎 限量商业许可证 (Limited Commercial Fragment)
- **费用**: 50 $WIP (高价格)
- **商业使用**: ✅ 允许
- **衍生作品**: ❌ 不允许
- **转让性**: ❌ 不可转让
- **收益分成**: 30%
- **有效期**: ⏰ 1年
- **收益上限**: 1000 $WIP

## 安装和配置

### 1. 环境准备
```bash
# 安装依赖
npm install @story-protocol/core-sdk viem

# 配置环境变量
echo "WALLET_PRIVATE_KEY=你的钱包私钥" > .env
echo "RPC_PROVIDER_URL=你的RPC地址" >> .env
```

### 2. 脚本配置
编辑 `add-commercial-license-mainnet.ts` 中的 CONFIG 对象：

```typescript
const CONFIG = {
  // 网络设置
  useMainnet: false, // 设置为 true 使用主网

  // NFT 信息 (必须修改)
  nftContract: "0x你的NFT合约地址", // 替换为实际合约地址
  tokenId: "1", // 替换为实际 Token ID

  // 许可证选项
  includeCommercial: true, // 是否包含商业使用许可证
  includeLimitedCommercial: true, // 是否包含限量商业许可证

  // 高级选项
  forceRegisterNewLicenses: false // 是否强制注册新许可证条款
};
```

## 使用方法

### 基本使用
```bash
# 运行脚本
npx ts-node add-commercial-license-mainnet.ts
```

### 编程方式使用
```typescript
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

// 创建管理器实例
const manager = new MainnetCommercialLicenseManager(
  process.env.WALLET_PRIVATE_KEY!,
  true // 使用主网
);

// 为 NFT 添加商业许可证
const result = await manager.addCommercialLicensesToNFT(
  "0xNFT合约地址",
  "1", // Token ID
  true, // 包含商业使用许可证
  true, // 包含限量商业许可证
  false // 不强制注册新许可证
);

console.log("IP ID:", result.ipId);
console.log("商业许可证 ID:", result.commercialLicenseTermsId);
console.log("限量商业许可证 ID:", result.limitedCommercialLicenseTermsId);
```

## 脚本执行流程

### 自动化流程
1. **许可证条款注册**: 注册商业使用和限量商业许可证条款
2. **IP 资产检查**: 检查 NFT 是否已注册为 IP 资产
3. **IP 资产注册**: 如果未注册，自动注册为 IP 资产
4. **许可证附加**: 将许可证条款附加到 IP 资产
5. **结果展示**: 显示完整的操作结果和许可证详情

### 输出示例
```
🚀 开始为 NFT 添加商业许可证...
============================================================
NFT 合约地址: 0x1234...
Token ID: 1
包含商业使用许可证: 是
包含限量商业许可证: 是

🔄 注册商业使用许可证条款...
✅ 商业使用许可证条款已注册
   许可证条款 ID: 123
   交易哈希: 0xabc...

🔄 注册限量商业许可证条款...
✅ 限量商业许可证条款已注册
   许可证条款 ID: 124
   交易哈希: 0xdef...

🔍 检查 NFT 是否已注册为 IP 资产...
✅ NFT 已注册为 IP 资产
   IP ID: 0x5678...

🔗 为 IP 资产附加商业使用许可证...
✅ 商业使用许可证已成功附加到 IP 资产

🔗 为 IP 资产附加限量商业许可证...
✅ 限量商业许可证已成功附加到 IP 资产

🎉 商业许可证添加完成！
============================================================
✅ IP 资产 ID: 0x5678...
✅ 商业使用许可证条款 ID: 123
✅ 限量商业许可证条款 ID: 124
✅ 是否为新注册的 IP: 否

📋 商业使用许可证详情:
   • 类型: 商业使用许可证 (商业权限碎片)
   • 费用: 10 $WIP
   • 商业使用: ✅ 允许
   • 衍生作品: ❌ 不允许
   • 转让性: ✅ 可转让
   • 收益分成: 15%
   • 有效期: ♾️ 永久有效

📋 限量商业许可证详情:
   • 类型: 限量商业许可证 (限量商业权限碎片)
   • 费用: 50 $WIP
   • 商业使用: ✅ 允许
   • 衍生作品: ❌ 不允许
   • 转让性: ❌ 不可转让
   • 收益分成: 30%
   • 收益上限: 1000 $WIP
   • 有效期: ⏰ 1年

🌐 浏览器查看:
   https://explorer.story.foundation/ipa/0x5678...
```

## 高级功能

### 单独添加许可证类型
```typescript
// 只添加商业使用许可证
await manager.addCommercialLicensesToNFT(
  nftContract,
  tokenId,
  true,  // 包含商业使用
  false, // 不包含限量商业
  false
);

// 只添加限量商业许可证
await manager.addCommercialLicensesToNFT(
  nftContract,
  tokenId,
  false, // 不包含商业使用
  true,  // 包含限量商业
  false
);
```

### 批量处理多个 NFT
```typescript
const nfts = [
  { contract: "0x123...", tokenId: "1" },
  { contract: "0x123...", tokenId: "2" },
  { contract: "0x456...", tokenId: "1" }
];

for (const nft of nfts) {
  try {
    await manager.addCommercialLicensesToNFT(
      nft.contract,
      nft.tokenId,
      true,
      true,
      false
    );
    console.log(`✅ NFT ${nft.contract}:${nft.tokenId} 处理完成`);
  } catch (error) {
    console.error(`❌ NFT ${nft.contract}:${nft.tokenId} 处理失败:`, error);
  }
}
```

## 注意事项

### ⚠️ 重要提醒
1. **主网操作**: 在主网上操作前，请确保在测试网上充分测试
2. **私钥安全**: 妥善保管私钥，不要提交到代码仓库
3. **Gas 费用**: 主网操作需要支付 Gas 费用，确保钱包有足够余额
4. **合约地址**: 确保使用正确的主网合约地址

### 🔧 故障排除
- **"NFT 合约地址未配置"**: 修改 CONFIG 中的 nftContract 字段
- **"私钥未配置"**: 在 .env 文件中设置 WALLET_PRIVATE_KEY
- **"余额不足"**: 确保钱包有足够的 ETH 支付 Gas 费用
- **"许可证已存在"**: 许可证可能已经附加，检查 IP 资产状态

## 相关文档
- [Personal License 脚本](./add-personal-license-mainnet.ts)
- [单张形象授权文档](./SINGLE_IMAGE_LICENSING.md)
- [Story Protocol 官方文档](https://docs.story.foundation/)
