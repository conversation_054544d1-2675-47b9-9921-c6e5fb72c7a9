import { parseEther, zeroAddress } from "viem";
import type { LicenseTerms } from "@story-protocol/core-sdk";

// 常用代币地址配置
export const SUPPORTED_CURRENCIES = {
  // Story Protocol 官方代币
  WIP: "******************************************",
  
  // 测试网代币 (Aeneid Testnet)
  MERC20: "******************************************", // 测试用 ERC20
  
  // 主网常用代币 (需要在主网部署时更新地址)
  USDC: "******************************************", // 示例地址
  USDT: "******************************************", // 示例地址
  WETH: "******************************************", // 示例地址
  
  // 无货币 (免费许可证)
  NONE: zeroAddress
};

// 多货币许可证类型配置
export const MultiCurrencyLicenseTypes = {
  // 1. 免费个人使用 - 无需支付
  FREE_PERSONAL: {
    transferable: false,
    royaltyPolicy: zeroAddress,
    defaultMintingFee: 0n,
    expiration: 0n,
    commercialUse: false,
    commercialAttribution: false,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 0,
    commercialRevCeiling: 0n,
    derivativesAllowed: false,
    derivativesAttribution: false,
    derivativesApproval: false,
    derivativesReciprocal: false,
    derivativeRevCeiling: 0n,
    currency: SUPPORTED_CURRENCIES.NONE,
    uri: "https://example.com/free-personal.json"
  } as LicenseTerms,

  // 2. WIP 支付的商业许可证
  COMMERCIAL_WIP: {
    transferable: true,
    royaltyPolicy: "******************************************",
    defaultMintingFee: parseEther("10"), // 10 WIP
    expiration: 0n,
    commercialUse: true,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 15,
    commercialRevCeiling: 0n,
    derivativesAllowed: false,
    derivativesAttribution: false,
    derivativesApproval: false,
    derivativesReciprocal: false,
    derivativeRevCeiling: 0n,
    currency: SUPPORTED_CURRENCIES.WIP,
    uri: "https://example.com/commercial-wip.json"
  } as LicenseTerms,

  // 3. USDC 支付的商业许可证 (稳定币)
  COMMERCIAL_USDC: {
    transferable: true,
    royaltyPolicy: "******************************************",
    defaultMintingFee: parseEther("50"), // 50 USDC (假设 USDC 也是 18 位小数)
    expiration: 0n,
    commercialUse: true,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 20, // 更高的收益分成
    commercialRevCeiling: 0n,
    derivativesAllowed: false,
    derivativesAttribution: false,
    derivativesApproval: false,
    derivativesReciprocal: false,
    derivativeRevCeiling: 0n,
    currency: SUPPORTED_CURRENCIES.USDC,
    uri: "https://example.com/commercial-usdc.json"
  } as LicenseTerms,

  // 4. ETH 支付的高端许可证
  PREMIUM_ETH: {
    transferable: true,
    royaltyPolicy: "******************************************",
    defaultMintingFee: parseEther("0.1"), // 0.1 ETH
    expiration: BigInt(365 * 24 * 60 * 60), // 1年
    commercialUse: true,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 25,
    commercialRevCeiling: parseEther("10"), // 10 ETH 上限
    derivativesAllowed: true,
    derivativesAttribution: true,
    derivativesApproval: false,
    derivativesReciprocal: true,
    derivativeRevCeiling: 0n,
    currency: SUPPORTED_CURRENCIES.WETH,
    uri: "https://example.com/premium-eth.json"
  } as LicenseTerms,

  // 5. 自定义代币支付的特殊许可证
  CUSTOM_TOKEN: {
    transferable: false,
    royaltyPolicy: "******************************************",
    defaultMintingFee: parseEther("1000"), // 1000 自定义代币
    expiration: 0n,
    commercialUse: true,
    commercialAttribution: true,
    commercializerChecker: zeroAddress,
    commercializerCheckerData: "0x",
    commercialRevShare: 30,
    commercialRevCeiling: 0n,
    derivativesAllowed: false,
    derivativesAttribution: false,
    derivativesApproval: true, // 需要批准
    derivativesReciprocal: false,
    derivativeRevCeiling: 0n,
    currency: "0x你的自定义代币地址", // 替换为实际的代币地址
    uri: "https://example.com/custom-token.json"
  } as LicenseTerms
};

// 货币信息配置
export const CurrencyInfo = {
  [SUPPORTED_CURRENCIES.WIP]: {
    name: "WIP",
    symbol: "WIP",
    decimals: 18,
    description: "Story Protocol 官方许可证代币"
  },
  [SUPPORTED_CURRENCIES.USDC]: {
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6, // USDC 通常是 6 位小数
    description: "美元稳定币，适合商业交易"
  },
  [SUPPORTED_CURRENCIES.USDT]: {
    name: "Tether",
    symbol: "USDT",
    decimals: 6,
    description: "另一种美元稳定币选择"
  },
  [SUPPORTED_CURRENCIES.WETH]: {
    name: "Wrapped Ether",
    symbol: "WETH",
    decimals: 18,
    description: "包装的以太坊，适合高价值交易"
  },
  [SUPPORTED_CURRENCIES.NONE]: {
    name: "Free",
    symbol: "FREE",
    decimals: 0,
    description: "免费许可证，无需支付"
  }
};

// 根据货币类型格式化金额
export function formatCurrencyAmount(amount: bigint, currency: string): string {
  const info = CurrencyInfo[currency];
  if (!info) return amount.toString();
  
  const divisor = BigInt(10 ** info.decimals);
  const formatted = Number(amount) / Number(divisor);
  
  return `${formatted} ${info.symbol}`;
}

// 获取货币信息
export function getCurrencyInfo(currency: string) {
  return CurrencyInfo[currency] || {
    name: "Unknown Token",
    symbol: "UNK",
    decimals: 18,
    description: "未知代币"
  };
}

// 验证货币地址是否支持
export function isSupportedCurrency(currency: string): boolean {
  return Object.values(SUPPORTED_CURRENCIES).includes(currency);
}

// 获取推荐的许可证类型基于用户需求
export function getRecommendedLicenseType(
  useCase: "personal" | "commercial" | "premium" | "custom",
  preferredCurrency?: string
): LicenseTerms {
  switch (useCase) {
    case "personal":
      return MultiCurrencyLicenseTypes.FREE_PERSONAL;
    
    case "commercial":
      if (preferredCurrency === SUPPORTED_CURRENCIES.USDC) {
        return MultiCurrencyLicenseTypes.COMMERCIAL_USDC;
      }
      return MultiCurrencyLicenseTypes.COMMERCIAL_WIP;
    
    case "premium":
      return MultiCurrencyLicenseTypes.PREMIUM_ETH;
    
    case "custom":
      return MultiCurrencyLicenseTypes.CUSTOM_TOKEN;
    
    default:
      return MultiCurrencyLicenseTypes.FREE_PERSONAL;
  }
}

// 示例：创建多货币许可证配置
export function createMultiCurrencyLicenseConfig() {
  console.log("🌍 多货币许可证配置示例:");
  console.log("=" .repeat(50));
  
  Object.entries(MultiCurrencyLicenseTypes).forEach(([type, config]) => {
    const currencyInfo = getCurrencyInfo(config.currency);
    const feeFormatted = formatCurrencyAmount(config.defaultMintingFee, config.currency);
    
    console.log(`\n${type}:`);
    console.log(`  💰 费用: ${feeFormatted}`);
    console.log(`  🪙 货币: ${currencyInfo.name} (${currencyInfo.symbol})`);
    console.log(`  💼 商业使用: ${config.commercialUse ? "✅" : "❌"}`);
    console.log(`  🎨 衍生作品: ${config.derivativesAllowed ? "✅" : "❌"}`);
    console.log(`  📈 收益分成: ${config.commercialRevShare}%`);
  });
  
  console.log("\n💡 使用建议:");
  console.log("  • 个人用户 → FREE_PERSONAL (免费)");
  console.log("  • 小企业 → COMMERCIAL_WIP (WIP 支付)");
  console.log("  • 国际企业 → COMMERCIAL_USDC (稳定币)");
  console.log("  • 高端项目 → PREMIUM_ETH (ETH 支付)");
  console.log("  • 特殊需求 → CUSTOM_TOKEN (自定义代币)");
}

// 运行示例
if (require.main === module) {
  createMultiCurrencyLicenseConfig();
}
