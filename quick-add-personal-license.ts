#!/usr/bin/env bun
/**
 * 快速为指定 NFT 添加个人使用许可证脚本
 *
 * 使用方法:
 * 1. 配置 .env 文件中的 WALLET_PRIVATE_KEY
 * 2. 修改下面的 NFT_CONFIG 配置
 * 3. 运行: bun run quick-add-personal-license.ts
 */

import { MainnetPersonalLicenseManager } from "./add-personal-license-mainnet";
import { getNetworkConfig, displayNetworkInfo, validateNetworkConfig } from "./network-config";
import type { Address } from "viem/accounts";

// 🔧 配置区域 - 请根据实际情况修改
const NFT_CONFIG = {
  // ⚠️ 重要：设置为 true 使用主网，false 使用测试网
  useMainnet: false,

  // 📝 NFT 信息 - 请替换为实际值
  nftContract: "******************************************" as Address, // 使用我们测试中创建的 NFT 合约
  tokenId: "1", // NFT 的 Token ID

  // 🔄 高级选项
  forceRegisterNewLicense: false, // 是否强制注册新的许可证条款
};

// 验证配置
function validateConfig() {
  const errors: string[] = [];

  // 检查私钥
  if (!process.env.WALLET_PRIVATE_KEY) {
    errors.push("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  // 检查 NFT 合约地址格式
  if (!NFT_CONFIG.nftContract || NFT_CONFIG.nftContract.length !== 42) {
    errors.push("❌ 请配置正确的 NFT 合约地址 (42 字符，以 0x 开头)");
  }

  // 检查 Token ID
  if (!NFT_CONFIG.tokenId || NFT_CONFIG.tokenId === "") {
    errors.push("❌ 请配置正确的 Token ID");
  }

  // 检查主网配置
  if (NFT_CONFIG.useMainnet) {
    const config = getNetworkConfig(true);
    if (!validateNetworkConfig(config)) {
      errors.push("❌ 主网合约地址配置不完整，请更新 network-config.ts");
    }
  }

  return errors;
}

// 显示配置信息
function displayConfig() {
  console.log("🔧 当前配置:");
  console.log("=" .repeat(50));
  console.log(`网络: ${NFT_CONFIG.useMainnet ? "主网" : "测试网"}`);
  console.log(`NFT 合约: ${NFT_CONFIG.nftContract}`);
  console.log(`Token ID: ${NFT_CONFIG.tokenId}`);
  console.log(`强制注册新许可证: ${NFT_CONFIG.forceRegisterNewLicense ? "是" : "否"}`);
  console.log("");
}

// 主执行函数
async function executeAddPersonalLicense() {
  console.log("🚀 快速添加个人使用许可证工具");
  console.log("=" .repeat(60));
  console.log("");

  // 1. 验证配置
  const configErrors = validateConfig();
  if (configErrors.length > 0) {
    console.error("配置错误:");
    configErrors.forEach(error => console.error(error));
    console.log("");
    console.log("💡 请修改脚本顶部的 NFT_CONFIG 配置");
    process.exit(1);
  }

  // 2. 显示配置和网络信息
  displayConfig();
  displayNetworkInfo(NFT_CONFIG.useMainnet);
  console.log("");

  // 3. 确认操作
  if (NFT_CONFIG.useMainnet) {
    console.log("⚠️ 警告：您即将在主网上执行操作！");
    console.log("   这将消耗真实的 gas 费用");
    console.log("   请确保您的钱包有足够的余额");
    console.log("");
  }

  try {
    // 4. 创建管理器并执行操作
    console.log("🔄 初始化许可证管理器...");
    const manager = new MainnetPersonalLicenseManager(
      process.env.WALLET_PRIVATE_KEY!,
      NFT_CONFIG.useMainnet
    );

    console.log("🎯 开始添加个人使用许可证...");
    console.log("");

    const result = await manager.addPersonalLicenseToNFT(
      NFT_CONFIG.nftContract,
      NFT_CONFIG.tokenId,
      NFT_CONFIG.forceRegisterNewLicense
    );

    // 5. 显示成功结果
    console.log("");
    console.log("🎉 操作成功完成！");
    console.log("=" .repeat(60));
    console.log("");
    console.log("📊 操作结果:");
    console.log(`   ✅ IP 资产 ID: ${result.ipId}`);
    console.log(`   ✅ 许可证条款 ID: ${result.licenseTermsId}`);
    console.log(`   ✅ 新注册的 IP: ${result.isNewIP ? "是" : "否"}`);
    console.log("");

    const networkConfig = getNetworkConfig(NFT_CONFIG.useMainnet);
    console.log("🔗 相关链接:");
    console.log(`   IP 资产浏览器: ${networkConfig.explorer}/ipa/${result.ipId}`);
    console.log("");

    console.log("📋 添加的许可证详情:");
    console.log("   🏷️ 类型: 个人使用许可证 (基础权限碎片)");
    console.log("   💰 费用: 免费");
    console.log("   🏢 商业使用: ❌ 不允许");
    console.log("   🎨 衍生作品: ❌ 不允许");
    console.log("   🔄 可转让: ❌ 不可转让");
    console.log("   ⏰ 有效期: ♾️ 永久有效");
    console.log("");

    console.log("💡 下一步可以做什么:");
    console.log("   • 用户可以免费获取此 NFT 的个人使用许可证");
    console.log("   • 可以继续添加其他类型的许可证 (商业使用、衍生作品等)");
    console.log("   • 可以铸造许可证代币供用户获取");

  } catch (error) {
    console.error("");
    console.error("💥 操作失败:");
    console.error(error);
    console.log("");
    console.log("🔧 故障排除建议:");
    console.log("   1. 检查网络连接");
    console.log("   2. 确认钱包余额充足");
    console.log("   3. 验证 NFT 合约地址和 Token ID 正确");
    console.log("   4. 确认钱包私钥正确");

    if (NFT_CONFIG.useMainnet) {
      console.log("   5. 确认主网合约地址配置正确");
    }

    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  executeAddPersonalLicense().catch(console.error);
}

export { executeAddPersonalLicense };
