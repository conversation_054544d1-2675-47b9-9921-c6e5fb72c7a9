# 商业许可证授权脚本

基于现有的 personal license 脚本，为指定 collection 的指定 NFT 授权商业使用碎片和限量商业碎片的完整解决方案。

## 📁 文件结构

```
├── add-commercial-license-mainnet.ts    # 主要脚本：商业许可证授权
├── test-commercial-license.ts           # 测试脚本：验证许可证配置
├── commercial-license-example.ts        # 使用示例：演示如何使用
├── COMMERCIAL_LICENSE_GUIDE.md          # 详细使用指南
└── COMMERCIAL_LICENSE_README.md         # 本文件：快速开始
```

## 🚀 快速开始

### 1. 环境配置
```bash
# 确保已安装依赖
npm install @story-protocol/core-sdk viem

# 配置环境变量
echo "WALLET_PRIVATE_KEY=你的钱包私钥" > .env
echo "RPC_PROVIDER_URL=你的RPC地址" >> .env
```

### 2. 配置 NFT 信息
编辑 `add-commercial-license-mainnet.ts` 中的配置：

```typescript
const CONFIG = {
  useMainnet: false, // 设置为 true 使用主网
  nftContract: "0x你的NFT合约地址", // 替换为实际地址
  tokenId: "1", // 替换为实际 Token ID
  includeCommercial: true, // 是否包含商业使用许可证
  includeLimitedCommercial: true, // 是否包含限量商业许可证
};
```

### 3. 运行脚本
```bash
# 测试许可证配置
bun run test-commercial

# 运行主脚本
bun run add-commercial-mainnet

# 运行使用示例
bun run commercial-example
```

## 💎 许可证类型

### 🏢 商业使用许可证 (Commercial Use Fragment)
- **费用**: 10 $WIP
- **商业使用**: ✅ 允许
- **转让性**: ✅ 可转让
- **收益分成**: 15%
- **有效期**: ♾️ 永久有效
- **适用场景**: 标准商业授权，适合大多数商业用途

### 💎 限量商业许可证 (Limited Commercial Fragment)  
- **费用**: 50 $WIP (高价格)
- **商业使用**: ✅ 允许
- **转让性**: ❌ 不可转让
- **收益分成**: 30%
- **有效期**: ⏰ 1年
- **收益上限**: 1000 $WIP
- **适用场景**: 高价值限量授权，适合独家或限时商业用途

## 🛠️ 主要功能

### MainnetCommercialLicenseManager 类

```typescript
// 创建管理器
const manager = new MainnetCommercialLicenseManager(privateKey, useMainnet);

// 为 NFT 添加商业许可证
const result = await manager.addCommercialLicensesToNFT(
  nftContract,
  tokenId,
  includeCommercial,      // 是否包含商业使用许可证
  includeLimitedCommercial, // 是否包含限量商业许可证
  forceRegisterNew        // 是否强制注册新许可证
);
```

### 核心方法
- `registerCommercialUseLicense()` - 注册商业使用许可证条款
- `registerLimitedCommercialLicense()` - 注册限量商业许可证条款
- `checkIfNFTIsRegisteredAsIP()` - 检查 NFT 是否已注册为 IP
- `registerNFTAsIP()` - 将 NFT 注册为 IP 资产
- `attachCommercialLicenseToIP()` - 附加商业使用许可证
- `attachLimitedCommercialLicenseToIP()` - 附加限量商业许可证

## 📋 使用场景

### 场景 1: 只添加商业使用许可证
```typescript
await manager.addCommercialLicensesToNFT(
  nftContract, tokenId,
  true,  // 包含商业使用
  false, // 不包含限量商业
  false
);
```

### 场景 2: 只添加限量商业许可证
```typescript
await manager.addCommercialLicensesToNFT(
  nftContract, tokenId,
  false, // 不包含商业使用
  true,  // 包含限量商业
  false
);
```

### 场景 3: 同时添加两种许可证
```typescript
await manager.addCommercialLicensesToNFT(
  nftContract, tokenId,
  true, // 包含商业使用
  true, // 包含限量商业
  false
);
```

### 场景 4: 批量处理多个 NFT
```typescript
import { batchAddCommercialLicenses } from "./commercial-license-example";

const nfts = [
  { contract: "0x123...", tokenId: "1", includeCommercial: true },
  { contract: "0x123...", tokenId: "2", includeLimitedCommercial: true },
  { contract: "0x456...", tokenId: "1", includeCommercial: true, includeLimitedCommercial: true }
];

const results = await batchAddCommercialLicenses(nfts, false);
```

## 🔍 测试和验证

### 运行测试
```bash
bun run test-commercial
```

测试内容包括：
- ✅ 许可证配置验证
- ✅ 字段完整性检查  
- ✅ 业务逻辑合理性检查
- ✅ 许可证类型对比
- ✅ 配置摘要展示

### 预期输出
```
🎉 所有测试通过！商业许可证配置正确。

📋 配置摘要:
• 商业使用许可证: 10 WIP, 15% 分成, 可转让, 永久有效
• 限量商业许可证: 50 WIP, 30% 分成, 不可转让, 1年有效, 1000 WIP 上限

✅ 可以安全使用 add-commercial-license-mainnet.ts 脚本
```

## ⚠️ 注意事项

### 主网使用
- 在主网操作前，请在测试网充分测试
- 确保钱包有足够的 ETH 支付 Gas 费用
- 妥善保管私钥，不要提交到代码仓库

### 配置检查
- 确保 NFT 合约地址正确
- 确认 Token ID 存在
- 验证环境变量配置

### 错误处理
- 脚本包含完整的错误处理和重试机制
- 如果许可证已存在，会跳过重复操作
- 详细的日志输出帮助调试问题

## 📚 相关文档

- [详细使用指南](./COMMERCIAL_LICENSE_GUIDE.md) - 完整的使用说明和配置指南
- [Personal License 脚本](./add-personal-license-mainnet.ts) - 个人使用许可证脚本
- [单张形象授权文档](./SINGLE_IMAGE_LICENSING.md) - 单张形象授权功能说明
- [Story Protocol 官方文档](https://docs.story.foundation/) - 官方开发文档

## 🎯 总结

本脚本提供了完整的商业许可证授权解决方案，支持：

✅ **商业使用许可证** - 标准商业授权，10 WIP，15% 分成，可转让，永久有效  
✅ **限量商业许可证** - 高价值限量授权，50 WIP，30% 分成，不可转让，1年有效  
✅ **灵活配置** - 可单独或组合添加不同类型的许可证  
✅ **批量处理** - 支持批量处理多个 NFT  
✅ **完整测试** - 包含全面的测试和验证机制  
✅ **详细文档** - 提供完整的使用指南和示例  

基于现有的 personal license 脚本架构，确保了代码的一致性和可维护性。
