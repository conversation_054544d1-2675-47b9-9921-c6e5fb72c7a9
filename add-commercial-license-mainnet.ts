import { http, parseEther } from "viem";
import { privateKeyToAccount, type Account, type Address } from "viem/accounts";
import { StoryClient, type StoryConfig, type LicenseTerms } from "@story-protocol/core-sdk";
import { zeroAddress } from "viem";

// 主网配置
const MAINNET_CONFIG = {
  chainId: "1516", // Story Protocol 主网链 ID
  rpcUrl: "https://rpc.story.foundation", // 主网 RPC
  // 主网合约地址 (需要根据实际部署地址更新)
  contracts: {
    royaltyPolicyLAP: "******************************************" as Address, // 主网版税政策地址
    wipToken: "******************************************" as Address // 主网WIP代币地址
  }
};

// 商业使用许可证配置 (付费商业权限碎片)
const COMMERCIAL_USE_LICENSE: LicenseTerms = {
  transferable: true, // 可转让
  royaltyPolicy: MAINNET_CONFIG.contracts.royaltyPolicyLAP, // 版税政策
  defaultMintingFee: parseEther("10"), // 10 $WIP
  expiration: 0n, // 永不过期
  commercialUse: true, // 允许商业使用
  commercialAttribution: true, // 需要商业署名
  commercializerChecker: zeroAddress,
  commercializerCheckerData: "0x",
  commercialRevShare: 15, // 15% 收益分成
  commercialRevCeiling: 0n, // 无收益上限
  derivativesAllowed: false, // 不允许衍生作品
  derivativesAttribution: false, // 不需要衍生署名
  derivativesApproval: false,
  derivativesReciprocal: false,
  derivativeRevCeiling: 0n,
  currency: MAINNET_CONFIG.contracts.wipToken, // 使用 WIP 代币支付
  uri: "https://github.com/piplabs/pil-document/blob/main/off-chain-terms/CommercialUse.json"
};

// 限量商业许可证配置 (高价限时商业权限碎片)
const LIMITED_COMMERCIAL_LICENSE: LicenseTerms = {
  transferable: false, // 不可转让
  royaltyPolicy: MAINNET_CONFIG.contracts.royaltyPolicyLAP, // 版税政策
  defaultMintingFee: parseEther("50"), // 50 $WIP 高价格
  expiration: BigInt(365 * 24 * 60 * 60), // 1年有效期
  commercialUse: true, // 允许商业使用
  commercialAttribution: true, // 需要商业署名
  commercializerChecker: zeroAddress,
  commercializerCheckerData: "0x",
  commercialRevShare: 30, // 30% 收益分成
  commercialRevCeiling: parseEther("1000"), // 最高收益上限 1000 WIP
  derivativesAllowed: false, // 不允许衍生作品
  derivativesAttribution: false, // 不需要衍生署名
  derivativesApproval: false,
  derivativesReciprocal: false,
  derivativeRevCeiling: 0n,
  currency: MAINNET_CONFIG.contracts.wipToken, // 使用 WIP 代币支付
  uri: "https://github.com/piplabs/pil-document/blob/main/off-chain-terms/LimitedCommercial.json"
};

// 商业许可证管理类
export class MainnetCommercialLicenseManager {
  private client: StoryClient;
  private commercialLicenseTermsId?: bigint;
  private limitedCommercialLicenseTermsId?: bigint;
  private chainId: bigint;

  constructor(privateKey: string, useMainnet: boolean = false) {
    const account: Account = privateKeyToAccount(`0x${privateKey}` as Address);

    const config: StoryConfig = {
      account: account,
      transport: http(useMainnet ? MAINNET_CONFIG.rpcUrl : process.env.RPC_PROVIDER_URL),
      chainId: useMainnet ? "mainnet" : "aeneid",
    };

    this.client = StoryClient.newClient(config);
    this.chainId = useMainnet ? BigInt(1516) : BigInt(1513); // 主网: 1516, 测试网: 1513
  }

  // 注册商业使用许可证条款
  async registerCommercialUseLicense(): Promise<bigint> {
    console.log("🔄 注册商业使用许可证条款...");

    try {
      const response = await this.client.license.registerPILTerms({
        ...COMMERCIAL_USE_LICENSE,
        txOptions: { waitForTransaction: true }
      });

      if (response.licenseTermsId) {
        this.commercialLicenseTermsId = response.licenseTermsId;
        console.log(`✅ 商业使用许可证条款已注册`);
        console.log(`   许可证条款 ID: ${response.licenseTermsId}`);
        console.log(`   交易哈希: ${response.txHash}`);
        return response.licenseTermsId;
      } else {
        throw new Error("注册商业使用许可证条款失败：未返回许可证条款 ID");
      }
    } catch (error) {
      console.error("❌ 注册商业使用许可证条款失败:", error);
      throw error;
    }
  }

  // 注册限量商业许可证条款
  async registerLimitedCommercialLicense(): Promise<bigint> {
    console.log("🔄 注册限量商业许可证条款...");

    try {
      const response = await this.client.license.registerPILTerms({
        ...LIMITED_COMMERCIAL_LICENSE,
        txOptions: { waitForTransaction: true }
      });

      if (response.licenseTermsId) {
        this.limitedCommercialLicenseTermsId = response.licenseTermsId;
        console.log(`✅ 限量商业许可证条款已注册`);
        console.log(`   许可证条款 ID: ${response.licenseTermsId}`);
        console.log(`   交易哈希: ${response.txHash}`);
        return response.licenseTermsId;
      } else {
        throw new Error("注册限量商业许可证条款失败：未返回许可证条款 ID");
      }
    } catch (error) {
      console.error("❌ 注册限量商业许可证条款失败:", error);
      throw error;
    }
  }

  // 检查 NFT 是否已注册为 IP 资产
  async checkIfNFTIsRegisteredAsIP(nftContract: Address, tokenId: string): Promise<Address | null> {
    try {
      console.log(`🔍 检查 NFT 是否已注册为 IP 资产...`);
      console.log(`   NFT 合约: ${nftContract}`);
      console.log(`   Token ID: ${tokenId}`);

      // 计算预期的 IP ID
      const ipId = await this.client.ipAsset.ipAssetRegistryClient.ipId({
        chainId: this.chainId,
        tokenContract: nftContract,
        tokenId: BigInt(tokenId)
      });

      if (ipId && ipId !== zeroAddress) {
        // 验证 IP 是否真正注册
        const isRegistered = await this.client.ipAsset.isRegistered(ipId);

        if (isRegistered) {
          console.log(`✅ NFT 已注册为 IP 资产`);
          console.log(`   IP ID: ${ipId}`);
          return ipId;
        } else {
          console.log(`ℹ️ NFT 尚未注册为 IP 资产 (计算出的 IP ID 未注册)`);
          return null;
        }
      } else {
        console.log(`ℹ️ NFT 尚未注册为 IP 资产 (无效的 IP ID)`);
        return null;
      }
    } catch (error) {
      console.log(`ℹ️ NFT 尚未注册为 IP 资产 (检查失败):`, error);
      return null;
    }
  }

  // 将 NFT 注册为 IP 资产
  async registerNFTAsIP(nftContract: Address, tokenId: string): Promise<Address> {
    console.log(`📝 将 NFT 注册为 IP 资产...`);

    try {
      const response = await this.client.ipAsset.register({
        nftContract,
        tokenId,
        txOptions: { waitForTransaction: true }
      });

      if (response.ipId) {
        console.log(`✅ NFT 已成功注册为 IP 资产`);
        console.log(`   IP ID: ${response.ipId}`);
        console.log(`   交易哈希: ${response.txHash}`);
        return response.ipId;
      } else {
        throw new Error("注册 IP 资产失败：未返回 IP ID");
      }
    } catch (error) {
      console.error("❌ 注册 IP 资产失败:", error);
      throw error;
    }
  }

  // 为 IP 资产附加商业使用许可证
  async attachCommercialLicenseToIP(ipId: Address): Promise<void> {
    if (!this.commercialLicenseTermsId) {
      throw new Error("请先注册商业使用许可证条款");
    }

    console.log(`🔗 为 IP 资产附加商业使用许可证...`);
    console.log(`   IP ID: ${ipId}`);
    console.log(`   许可证条款 ID: ${this.commercialLicenseTermsId}`);

    try {
      const response = await this.client.license.attachLicenseTerms({
        licenseTermsId: this.commercialLicenseTermsId.toString(),
        ipId,
        txOptions: { waitForTransaction: true },
      });

      if (response.success) {
        console.log(`✅ 商业使用许可证已成功附加到 IP 资产`);
        console.log(`   交易哈希: ${response.txHash}`);
      } else {
        console.log(`ℹ️ 商业使用许可证可能已经附加到此 IP 资产`);
      }
    } catch (error) {
      console.error("❌ 附加商业使用许可证失败:", error);
      throw error;
    }
  }

  // 为 IP 资产附加限量商业许可证
  async attachLimitedCommercialLicenseToIP(ipId: Address): Promise<void> {
    if (!this.limitedCommercialLicenseTermsId) {
      throw new Error("请先注册限量商业许可证条款");
    }

    console.log(`🔗 为 IP 资产附加限量商业许可证...`);
    console.log(`   IP ID: ${ipId}`);
    console.log(`   许可证条款 ID: ${this.limitedCommercialLicenseTermsId}`);

    try {
      const response = await this.client.license.attachLicenseTerms({
        licenseTermsId: this.limitedCommercialLicenseTermsId.toString(),
        ipId,
        txOptions: { waitForTransaction: true },
      });

      if (response.success) {
        console.log(`✅ 限量商业许可证已成功附加到 IP 资产`);
        console.log(`   交易哈希: ${response.txHash}`);
      } else {
        console.log(`ℹ️ 限量商业许可证可能已经附加到此 IP 资产`);
      }
    } catch (error) {
      console.error("❌ 附加限量商业许可证失败:", error);
      throw error;
    }
  }

  // 完整流程：为指定 NFT 添加商业使用和限量商业许可证
  async addCommercialLicensesToNFT(
    nftContract: Address,
    tokenId: string,
    includeCommercial: boolean = true,
    includeLimitedCommercial: boolean = true,
    forceRegisterNewLicenses: boolean = false
  ): Promise<{
    ipId: Address;
    commercialLicenseTermsId?: bigint;
    limitedCommercialLicenseTermsId?: bigint;
    isNewIP: boolean;
  }> {
    console.log("🚀 开始为 NFT 添加商业许可证...");
    console.log("=" .repeat(60));
    console.log(`NFT 合约地址: ${nftContract}`);
    console.log(`Token ID: ${tokenId}`);
    console.log(`包含商业使用许可证: ${includeCommercial ? "是" : "否"}`);
    console.log(`包含限量商业许可证: ${includeLimitedCommercial ? "是" : "否"}`);
    console.log("");

    try {
      let commercialLicenseTermsId: bigint | undefined;
      let limitedCommercialLicenseTermsId: bigint | undefined;

      // 步骤 1: 注册或获取商业使用许可证条款
      if (includeCommercial) {
        if (forceRegisterNewLicenses || !this.commercialLicenseTermsId) {
          commercialLicenseTermsId = await this.registerCommercialUseLicense();
        } else {
          commercialLicenseTermsId = this.commercialLicenseTermsId;
          console.log(`ℹ️ 使用已有的商业使用许可证条款 ID: ${commercialLicenseTermsId}`);
        }
        console.log("");
      }

      // 步骤 2: 注册或获取限量商业许可证条款
      if (includeLimitedCommercial) {
        if (forceRegisterNewLicenses || !this.limitedCommercialLicenseTermsId) {
          limitedCommercialLicenseTermsId = await this.registerLimitedCommercialLicense();
        } else {
          limitedCommercialLicenseTermsId = this.limitedCommercialLicenseTermsId;
          console.log(`ℹ️ 使用已有的限量商业许可证条款 ID: ${limitedCommercialLicenseTermsId}`);
        }
        console.log("");
      }

      // 步骤 3: 检查 NFT 是否已注册为 IP
      let ipId = await this.checkIfNFTIsRegisteredAsIP(nftContract, tokenId);
      let isNewIP = false;
      console.log("");

      // 步骤 4: 如果未注册，则注册为 IP 资产
      if (!ipId) {
        ipId = await this.registerNFTAsIP(nftContract, tokenId);
        isNewIP = true;
        console.log("");
      }

      // 步骤 5: 附加商业使用许可证
      if (includeCommercial && commercialLicenseTermsId) {
        await this.attachCommercialLicenseToIP(ipId);
        console.log("");
      }

      // 步骤 6: 附加限量商业许可证
      if (includeLimitedCommercial && limitedCommercialLicenseTermsId) {
        await this.attachLimitedCommercialLicenseToIP(ipId);
        console.log("");
      }

      // 步骤 7: 显示结果
      console.log("🎉 商业许可证添加完成！");
      console.log("=" .repeat(60));
      console.log(`✅ IP 资产 ID: ${ipId}`);
      if (commercialLicenseTermsId) {
        console.log(`✅ 商业使用许可证条款 ID: ${commercialLicenseTermsId}`);
      }
      if (limitedCommercialLicenseTermsId) {
        console.log(`✅ 限量商业许可证条款 ID: ${limitedCommercialLicenseTermsId}`);
      }
      console.log(`✅ 是否为新注册的 IP: ${isNewIP ? "是" : "否"}`);
      console.log("");

      if (includeCommercial) {
        console.log("📋 商业使用许可证详情:");
        console.log("   • 类型: 商业使用许可证 (商业权限碎片)");
        console.log("   • 费用: 10 $WIP");
        console.log("   • 商业使用: ✅ 允许");
        console.log("   • 衍生作品: ❌ 不允许");
        console.log("   • 转让性: ✅ 可转让");
        console.log("   • 收益分成: 15%");
        console.log("   • 有效期: ♾️ 永久有效");
        console.log("");
      }

      if (includeLimitedCommercial) {
        console.log("📋 限量商业许可证详情:");
        console.log("   • 类型: 限量商业许可证 (限量商业权限碎片)");
        console.log("   • 费用: 50 $WIP");
        console.log("   • 商业使用: ✅ 允许");
        console.log("   • 衍生作品: ❌ 不允许");
        console.log("   • 转让性: ❌ 不可转让");
        console.log("   • 收益分成: 30%");
        console.log("   • 收益上限: 1000 $WIP");
        console.log("   • 有效期: ⏰ 1年");
        console.log("");
      }

      console.log("🌐 浏览器查看:");
      console.log(`   https://explorer.story.foundation/ipa/${ipId}`);

      return {
        ipId,
        commercialLicenseTermsId,
        limitedCommercialLicenseTermsId,
        isNewIP
      };

    } catch (error) {
      console.error("💥 添加商业许可证失败:", error);
      throw error;
    }
  }

  // 获取许可证条款 ID
  getCommercialLicenseTermsId(): bigint | undefined {
    return this.commercialLicenseTermsId;
  }

  getLimitedCommercialLicenseTermsId(): bigint | undefined {
    return this.limitedCommercialLicenseTermsId;
  }
}

// 主函数 - 使用示例
async function main() {
  // 配置参数 - 请根据实际情况修改
  const CONFIG = {
    // 是否使用主网 (true = 主网, false = 测试网)
    useMainnet: false, // ⚠️ 设置为 true 以使用主网

    // NFT 信息
    nftContract: "0x你的NFT合约地址" as Address, // 替换为实际的 NFT 合约地址
    tokenId: "1", // 替换为实际的 Token ID

    // 许可证选项
    includeCommercial: true, // 是否包含商业使用许可证
    includeLimitedCommercial: true, // 是否包含限量商业许可证

    // 是否强制注册新的许可证条款
    forceRegisterNewLicenses: false
  };

  // 验证配置
  if (CONFIG.nftContract === "0x你的NFT合约地址") {
    console.error("❌ 请先配置正确的 NFT 合约地址");
    process.exit(1);
  }

  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    process.exit(1);
  }

  try {
    // 创建管理器实例
    const manager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      CONFIG.useMainnet
    );

    // 执行添加商业许可证流程
    const result = await manager.addCommercialLicensesToNFT(
      CONFIG.nftContract,
      CONFIG.tokenId,
      CONFIG.includeCommercial,
      CONFIG.includeLimitedCommercial,
      CONFIG.forceRegisterNewLicenses
    );

    console.log("🎯 操作成功完成！");
    console.log("");
    console.log("💡 下一步操作建议:");
    console.log("1. 用户可以通过支付 10 $WIP 铸造商业使用许可证代币");
    console.log("2. 用户可以通过支付 50 $WIP 铸造限量商业许可证代币");
    console.log("3. 商业使用许可证允许商业用途，收益分成 15%");
    console.log("4. 限量商业许可证限时1年，收益分成 30%，上限 1000 $WIP");

  } catch (error) {
    console.error("💥 操作失败:", error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

// 导出配置
export { COMMERCIAL_USE_LICENSE, LIMITED_COMMERCIAL_LICENSE };
